<?php
// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(SITE_URL . '/admin/login.php');
}

// الحصول على معلومات المستخدم الحالي
$current_user = getUserById($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $page_title ?? 'لوحة التحكم'; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/admin/assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="admin-layout">
    
    <!-- شريط التنقل العلوي -->
    <nav class="admin-navbar">
        <div class="navbar-content">
            <!-- شعار الموقع -->
            <div class="navbar-brand">
                <a href="<?php echo SITE_URL; ?>/admin/" class="brand-link">
                    <?php $logo = getSetting('site_logo'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo SITE_URL . '/' . $logo; ?>" alt="<?php echo getSetting('site_name'); ?>" class="brand-logo">
                    <?php else: ?>
                        <i class="fas fa-newspaper brand-icon"></i>
                    <?php endif; ?>
                    <span class="brand-text"><?php echo getSetting('site_name', 'موقع الأخبار'); ?></span>
                </a>
            </div>
            
            <!-- زر القائمة للهاتف المحمول -->
            <button class="mobile-menu-toggle" onclick="toggleSidebar()">
                <span></span>
                <span></span>
                <span></span>
            </button>
            
            <!-- عناصر الشريط العلوي -->
            <div class="navbar-items">
                <!-- البحث السريع -->
                <div class="navbar-search">
                    <form class="search-form" action="<?php echo SITE_URL; ?>/admin/search.php" method="GET">
                        <input type="text" name="q" class="search-input" placeholder="بحث سريع..." autocomplete="off">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <!-- الإشعارات -->
                <div class="navbar-notifications">
                    <div class="notification-dropdown">
                        <button class="notification-btn" onclick="toggleNotifications()">
                            <i class="fas fa-bell"></i>
                            <?php 
                            // حساب الإشعارات
                            $db->query("SELECT COUNT(*) as total FROM comments WHERE status = 'pending'");
                            $pending_notifications = $db->single()['total'];
                            if ($pending_notifications > 0):
                            ?>
                            <span class="notification-badge"><?php echo $pending_notifications; ?></span>
                            <?php endif; ?>
                        </button>
                        
                        <div class="notification-menu" id="notificationMenu">
                            <div class="notification-header">
                                <h4>الإشعارات</h4>
                            </div>
                            <div class="notification-list">
                                <?php if ($pending_notifications > 0): ?>
                                <a href="<?php echo SITE_URL; ?>/admin/comments.php" class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">تعليقات جديدة</div>
                                        <div class="notification-text"><?php echo $pending_notifications; ?> تعليق في انتظار المراجعة</div>
                                    </div>
                                </a>
                                <?php else: ?>
                                <div class="notification-empty">
                                    <i class="fas fa-bell-slash"></i>
                                    <p>لا توجد إشعارات جديدة</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- ملف المستخدم -->
                <div class="navbar-user">
                    <div class="user-dropdown">
                        <button class="user-btn" onclick="toggleUserMenu()">
                            <div class="user-avatar">
                                <?php if ($current_user['avatar']): ?>
                                    <img src="<?php echo SITE_URL . '/' . $current_user['avatar']; ?>" alt="<?php echo htmlspecialchars($current_user['full_name']); ?>">
                                <?php else: ?>
                                    <i class="fas fa-user"></i>
                                <?php endif; ?>
                            </div>
                            <div class="user-info">
                                <div class="user-name"><?php echo htmlspecialchars($current_user['full_name'] ?: $current_user['username']); ?></div>
                                <div class="user-role">
                                    <?php 
                                    $role_labels = [
                                        'admin' => 'مدير',
                                        'editor' => 'محرر',
                                        'author' => 'كاتب'
                                    ];
                                    echo $role_labels[$current_user['role']] ?? $current_user['role'];
                                    ?>
                                </div>
                            </div>
                            <i class="fas fa-chevron-down user-arrow"></i>
                        </button>
                        
                        <div class="user-menu" id="userMenu">
                            <a href="<?php echo SITE_URL; ?>/admin/profile.php" class="user-menu-item">
                                <i class="fas fa-user"></i>
                                الملف الشخصي
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="user-menu-item">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                            <div class="user-menu-divider"></div>
                            <a href="<?php echo SITE_URL; ?>" class="user-menu-item" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                عرض الموقع
                            </a>
                            <a href="<?php echo SITE_URL; ?>/admin/logout.php" class="user-menu-item logout">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- الشريط الجانبي -->
    <aside class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-content">
            <!-- قائمة التنقل -->
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    
                    <li class="nav-section">
                        <span class="nav-section-title">إدارة المحتوى</span>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/articles.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'articles.php' ? 'active' : ''; ?>">
                            <i class="fas fa-newspaper"></i>
                            <span>المقالات</span>
                            <?php if ($draft_articles ?? 0 > 0): ?>
                            <span class="nav-badge"><?php echo $draft_articles; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/categories.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'categories.php' ? 'active' : ''; ?>">
                            <i class="fas fa-tags"></i>
                            <span>الفئات</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/comments.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'comments.php' ? 'active' : ''; ?>">
                            <i class="fas fa-comments"></i>
                            <span>التعليقات</span>
                            <?php if (($pending_comments ?? 0) > 0): ?>
                            <span class="nav-badge"><?php echo $pending_comments; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    
                    <li class="nav-section">
                        <span class="nav-section-title">مصادر الأخبار</span>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/rss.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'rss.php' ? 'active' : ''; ?>">
                            <i class="fas fa-rss"></i>
                            <span>مصادر RSS</span>
                        </a>
                    </li>
                    
                    <?php if (hasPermission('admin')): ?>
                    <li class="nav-section">
                        <span class="nav-section-title">إدارة النظام</span>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/users.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                            <i class="fas fa-users"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/settings.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                            <i class="fas fa-cog"></i>
                            <span>إعدادات الموقع</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-section">
                        <span class="nav-section-title">التحليلات والتقارير</span>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/admin/analytics.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'analytics.php' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-bar"></i>
                            <span>التحليلات</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        
        <!-- تذييل الشريط الجانبي -->
        <div class="sidebar-footer">
            <div class="sidebar-user">
                <div class="user-avatar-small">
                    <?php if ($current_user['avatar']): ?>
                        <img src="<?php echo SITE_URL . '/' . $current_user['avatar']; ?>" alt="<?php echo htmlspecialchars($current_user['full_name']); ?>">
                    <?php else: ?>
                        <i class="fas fa-user"></i>
                    <?php endif; ?>
                </div>
                <div class="user-details">
                    <div class="user-name-small"><?php echo htmlspecialchars(truncateText($current_user['full_name'] ?: $current_user['username'], 15)); ?></div>
                    <div class="user-status">متصل</div>
                </div>
            </div>
        </div>
    </aside>
    
    <!-- المحتوى الرئيسي -->
    <main class="admin-main">
        <div class="admin-container">

<style>
/* تنسيقات لوحة التحكم */
.admin-layout {
    background: #f8f9fa;
    font-family: 'Cairo', sans-serif;
}

/* الشريط العلوي */
.admin-navbar {
    background: var(--white-color);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1000;
    height: 70px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 20px;
}

.navbar-brand .brand-link {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 18px;
}

.brand-logo {
    height: 35px;
    width: auto;
}

.brand-icon {
    font-size: 24px;
    color: var(--secondary-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-color);
    margin: 3px 0;
    transition: var(--transition);
}

.navbar-items {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* البحث */
.navbar-search .search-form {
    display: flex;
    align-items: center;
    background: var(--light-color);
    border-radius: 25px;
    padding: 5px;
}

.search-input {
    border: none;
    background: none;
    padding: 8px 15px;
    font-size: 14px;
    width: 250px;
    outline: none;
}

.search-btn {
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #2980b9;
}

/* الإشعارات */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
    position: relative;
    padding: 10px;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--light-color);
}

.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: var(--accent-color);
    color: var(--white-color);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.notification-menu {
    position: absolute;
    top: 100%;
    left: -150px;
    width: 300px;
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.notification-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--primary-color);
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    text-decoration: none;
    color: var(--text-color);
    border-bottom: 1px solid var(--light-color);
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--light-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-title {
    font-weight: 500;
    margin-bottom: 3px;
}

.notification-text {
    font-size: 12px;
    color: var(--text-light);
}

.notification-empty {
    text-align: center;
    padding: 30px 20px;
    color: var(--text-light);
}

.notification-empty i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

/* ملف المستخدم */
.user-dropdown {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-btn:hover {
    background: var(--light-color);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--secondary-color);
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 500;
    font-size: 14px;
    color: var(--text-color);
}

.user-role {
    font-size: 12px;
    color: var(--text-light);
}

.user-arrow {
    font-size: 12px;
    color: var(--text-light);
    transition: var(--transition);
}

.user-btn.active .user-arrow {
    transform: rotate(180deg);
}

.user-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 200px;
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    transition: var(--transition);
}

.user-menu-item:hover {
    background: var(--light-color);
}

.user-menu-item.logout {
    color: var(--accent-color);
}

.user-menu-item.logout:hover {
    background: #fee;
}

.user-menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 5px 0;
}

/* الشريط الجانبي */
.admin-sidebar {
    position: fixed;
    top: 70px;
    right: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--white-color);
    border-left: 1px solid var(--border-color);
    z-index: 999;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-section {
    padding: 15px 20px 10px;
}

.nav-section-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    background: var(--light-color);
    color: var(--secondary-color);
}

.nav-link.active {
    background: var(--secondary-color);
    color: var(--white-color);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

.nav-badge {
    background: var(--accent-color);
    color: var(--white-color);
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    margin-right: auto;
}

/* تذييل الشريط الجانبي */
.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.sidebar-user {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar-small {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    overflow: hidden;
    background: var(--secondary-color);
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name-small {
    font-weight: 500;
    font-size: 13px;
    color: var(--text-color);
}

.user-status {
    font-size: 11px;
    color: var(--success-color);
}

/* المحتوى الرئيسي */
.admin-main {
    margin-top: 70px;
    margin-right: 280px;
    min-height: calc(100vh - 70px);
}

.admin-container {
    padding: 30px;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }
    
    .navbar-search {
        display: none;
    }
    
    .admin-sidebar {
        transform: translateX(100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
    }
    
    .admin-container {
        padding: 20px 15px;
    }
    
    .search-input {
        width: 150px;
    }
    
    .user-info {
        display: none;
    }
}
</style>

<script>
// وظائف JavaScript للوحة التحكم
function toggleSidebar() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('show');
}

function toggleNotifications() {
    const menu = document.getElementById('notificationMenu');
    menu.classList.toggle('show');
}

function toggleUserMenu() {
    const menu = document.getElementById('userMenu');
    const btn = document.querySelector('.user-btn');
    menu.classList.toggle('show');
    btn.classList.toggle('active');
}

// إغلاق القوائم عند النقر خارجها
document.addEventListener('click', function(e) {
    if (!e.target.closest('.notification-dropdown')) {
        document.getElementById('notificationMenu').classList.remove('show');
    }
    
    if (!e.target.closest('.user-dropdown')) {
        document.getElementById('userMenu').classList.remove('show');
        document.querySelector('.user-btn').classList.remove('active');
    }
});
</script>

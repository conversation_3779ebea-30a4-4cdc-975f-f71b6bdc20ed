<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// الحصول على كلمة البحث
$search_query = isset($_GET['q']) ? trim(sanitize($_GET['q'])) : '';
$category_filter = isset($_GET['category']) ? (int)$_GET['category'] : null;

// إعدادات الترقيم
$articles_per_page = (int)getSetting('articles_per_page', 10);
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($current_page - 1) * $articles_per_page;

// نتائج البحث
$search_results = [];
$total_results = 0;
$total_pages = 0;

if (!empty($search_query)) {
    // البحث في المقالات
    $search_results = searchArticles($search_query, $articles_per_page, $offset);
    $total_results = countArticles($category_filter, 'published', $search_query);
    $total_pages = ceil($total_results / $articles_per_page);
}

// الحصول على الفئات للفلترة
$categories = getCategories();

// إعدادات الصفحة
$page_title = !empty($search_query) ? 
    'نتائج البحث عن: ' . $search_query . ' - ' . getSetting('site_name', 'موقع الأخبار العربي') :
    'البحث - ' . getSetting('site_name', 'موقع الأخبار العربي');
$page_description = !empty($search_query) ? 
    'نتائج البحث عن "' . $search_query . '" في ' . getSetting('site_name', 'موقع الأخبار العربي') :
    'ابحث في جميع أخبار ومقالات ' . getSetting('site_name', 'موقع الأخبار العربي');

// تضمين الرأس
include 'includes/header.php';
?>

<div class="container">
    <!-- مسار التنقل -->
    <div class="breadcrumb-wrapper mb-4">
        <?php
        $breadcrumb_items = [
            ['title' => 'الرئيسية', 'url' => SITE_URL],
            ['title' => 'البحث', 'url' => '']
        ];
        
        echo renderBreadcrumb($breadcrumb_items);
        ?>
    </div>
    
    <div class="row">
        <!-- المحتوى الرئيسي -->
        <div class="col-lg-8 col-md-12">
            <!-- نموذج البحث المتقدم -->
            <div class="search-form-advanced">
                <h2 class="search-title">
                    <i class="fas fa-search"></i>
                    البحث في الأخبار
                </h2>
                
                <form class="advanced-search-form" method="GET" action="<?php echo SITE_URL; ?>/search.php">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="search-input" class="form-label">كلمة البحث</label>
                                <input type="text" id="search-input" name="q" class="form-control search-input-large" 
                                       placeholder="ابحث في العناوين والمحتوى..." 
                                       value="<?php echo htmlspecialchars($search_query); ?>" autocomplete="off">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="category-filter" class="form-label">الفئة</label>
                                <select id="category-filter" name="category" class="form-control">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" 
                                            <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="search-actions">
                        <button type="submit" class="btn btn-primary btn-search">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <a href="<?php echo SITE_URL; ?>/search.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            مسح
                        </a>
                    </div>
                </form>
            </div>
            
            <!-- نتائج البحث -->
            <?php if (!empty($search_query)): ?>
            <div class="search-results-section">
                <div class="results-header">
                    <h3 class="results-title">
                        <i class="fas fa-list"></i>
                        نتائج البحث
                    </h3>
                    <div class="results-info">
                        <?php if ($total_results > 0): ?>
                            تم العثور على <strong><?php echo number_format($total_results); ?></strong> نتيجة 
                            للبحث عن "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
                        <?php else: ?>
                            لم يتم العثور على نتائج للبحث عن "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if (!empty($search_results)): ?>
                <!-- عرض النتائج -->
                <div class="search-results-list">
                    <?php foreach ($search_results as $article): ?>
                    <div class="search-result-item">
                        <div class="result-image">
                            <?php 
                            $image_url = $article['image'] ? SITE_URL . '/' . $article['image'] : SITE_URL . '/assets/images/default-article.jpg';
                            ?>
                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                <img src="<?php echo $image_url; ?>" alt="<?php echo htmlspecialchars($article['title']); ?>" loading="lazy">
                            </a>
                            
                            <?php if ($article['category_name']): ?>
                            <span class="result-category" style="background-color: <?php echo $article['category_color']; ?>">
                                <?php echo htmlspecialchars($article['category_name']); ?>
                            </span>
                            <?php endif; ?>
                            
                            <?php if ($article['is_breaking']): ?>
                            <span class="breaking-badge">عاجل</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="result-content">
                            <h4 class="result-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                    <?php 
                                    // تمييز كلمة البحث في العنوان
                                    $highlighted_title = str_ireplace($search_query, '<mark>' . $search_query . '</mark>', htmlspecialchars($article['title']));
                                    echo $highlighted_title;
                                    ?>
                                </a>
                            </h4>
                            
                            <?php if ($article['excerpt']): ?>
                            <p class="result-excerpt">
                                <?php 
                                // تمييز كلمة البحث في المقتطف
                                $excerpt = truncateText($article['excerpt'], 150);
                                $highlighted_excerpt = str_ireplace($search_query, '<mark>' . $search_query . '</mark>', htmlspecialchars($excerpt));
                                echo $highlighted_excerpt;
                                ?>
                            </p>
                            <?php endif; ?>
                            
                            <div class="result-meta">
                                <span class="result-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                </span>
                                
                                <?php if ($article['views'] > 0): ?>
                                <span class="result-views">
                                    <i class="fas fa-eye"></i>
                                    <?php echo number_format($article['views']); ?> مشاهدة
                                </span>
                                <?php endif; ?>
                                
                                <?php if ($article['author_name']): ?>
                                <span class="result-author">
                                    <i class="fas fa-user"></i>
                                    <?php echo htmlspecialchars($article['author_name']); ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination-wrapper">
                    <?php 
                    $base_url = SITE_URL . '/search.php?q=' . urlencode($search_query);
                    if ($category_filter) {
                        $base_url .= '&category=' . $category_filter;
                    }
                    echo getPagination($current_page, $total_pages, $base_url); 
                    ?>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <!-- رسالة عدم وجود نتائج -->
                <div class="no-results">
                    <div class="no-results-message">
                        <i class="fas fa-search"></i>
                        <h4>لم يتم العثور على نتائج</h4>
                        <p>لم نتمكن من العثور على أي مقالات تحتوي على "<strong><?php echo htmlspecialchars($search_query); ?></strong>"</p>
                        
                        <div class="search-suggestions">
                            <h5>اقتراحات للبحث:</h5>
                            <ul>
                                <li>تأكد من صحة الكتابة</li>
                                <li>جرب كلمات مختلفة أو أكثر عمومية</li>
                                <li>استخدم كلمات مفتاحية أقل</li>
                                <li>جرب البحث في فئة مختلفة</li>
                            </ul>
                        </div>
                        
                        <div class="popular-searches">
                            <h5>عمليات بحث شائعة:</h5>
                            <div class="search-tags">
                                <a href="<?php echo SITE_URL; ?>/search.php?q=أخبار" class="search-tag">أخبار</a>
                                <a href="<?php echo SITE_URL; ?>/search.php?q=سياسة" class="search-tag">سياسة</a>
                                <a href="<?php echo SITE_URL; ?>/search.php?q=اقتصاد" class="search-tag">اقتصاد</a>
                                <a href="<?php echo SITE_URL; ?>/search.php?q=رياضة" class="search-tag">رياضة</a>
                                <a href="<?php echo SITE_URL; ?>/search.php?q=تكنولوجيا" class="search-tag">تكنولوجيا</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <?php else: ?>
            <!-- صفحة البحث الافتراضية -->
            <div class="search-welcome">
                <div class="welcome-content">
                    <i class="fas fa-search"></i>
                    <h3>ابحث في جميع أخبارنا</h3>
                    <p>استخدم نموذج البحث أعلاه للعثور على المقالات والأخبار التي تهمك</p>
                    
                    <div class="search-features">
                        <div class="feature-item">
                            <i class="fas fa-bolt"></i>
                            <span>بحث سريع ودقيق</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-filter"></i>
                            <span>فلترة حسب الفئة</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-highlight"></i>
                            <span>تمييز النتائج</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أحدث المقالات -->
            <div class="latest-articles-search">
                <h3 class="section-title">
                    <i class="fas fa-newspaper"></i>
                    أحدث المقالات
                </h3>
                
                <?php 
                $latest_articles = getArticles(6, 0, null, 'published');
                if (!empty($latest_articles)):
                ?>
                <div class="row">
                    <?php foreach ($latest_articles as $article): ?>
                    <div class="col-md-6 mb-3">
                        <?php echo renderArticleCard($article, false, true); ?>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4 col-md-12">
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>
</div>

<style>
/* تنسيقات صفحة البحث */
.search-form-advanced {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.search-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-title i {
    color: var(--secondary-color);
}

.search-input-large {
    font-size: 16px;
    padding: 15px 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-input-large:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.btn-search {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 500;
}

/* نتائج البحث */
.search-results-section {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.results-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
}

.results-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.results-title i {
    color: var(--secondary-color);
}

.results-info {
    color: var(--text-light);
    font-size: 14px;
}

.results-info strong {
    color: var(--text-color);
}

/* عنصر نتيجة البحث */
.search-result-item {
    display: flex;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.search-result-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.search-result-item:hover {
    background: var(--light-color);
    margin: 0 -15px;
    padding: 20px 15px;
    border-radius: var(--border-radius);
}

.result-image {
    width: 150px;
    height: 100px;
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    flex-shrink: 0;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.search-result-item:hover .result-image img {
    transform: scale(1.05);
}

.result-category {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    color: var(--white-color);
}

.result-content {
    flex: 1;
}

.result-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.result-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.result-title a:hover {
    color: var(--secondary-color);
}

.result-excerpt {
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 12px;
    color: var(--text-light);
}

.result-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.result-meta i {
    font-size: 11px;
}

/* تمييز النص */
mark {
    background: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

/* رسالة عدم وجود نتائج */
.no-results {
    text-align: center;
    padding: 50px 20px;
}

.no-results-message i {
    font-size: 64px;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-results-message h4 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 24px;
}

.no-results-message p {
    color: var(--text-light);
    margin-bottom: 30px;
    font-size: 16px;
}

.search-suggestions {
    background: var(--light-color);
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 25px;
    text-align: right;
}

.search-suggestions h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.search-suggestions ul {
    list-style: none;
    padding: 0;
}

.search-suggestions li {
    padding: 5px 0;
    color: var(--text-light);
    position: relative;
    padding-right: 20px;
}

.search-suggestions li::before {
    content: '•';
    color: var(--secondary-color);
    position: absolute;
    right: 0;
}

.popular-searches h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.search-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.search-tag {
    display: inline-block;
    padding: 6px 15px;
    background: var(--secondary-color);
    color: var(--white-color);
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
    transition: var(--transition);
}

.search-tag:hover {
    background: var(--primary-color);
    transform: translateY(-1px);
}

/* صفحة البحث الافتراضية */
.search-welcome {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 50px 30px;
    margin-bottom: 30px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.welcome-content i {
    font-size: 64px;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.welcome-content h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 28px;
}

.welcome-content p {
    color: var(--text-light);
    margin-bottom: 30px;
    font-size: 16px;
}

.search-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
    font-size: 14px;
}

.feature-item i {
    color: var(--secondary-color);
    font-size: 16px;
}

/* أحدث المقالات في البحث */
.latest-articles-search {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    border: 1px solid var(--border-color);
}

.section-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
}

.section-title i {
    color: var(--secondary-color);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .search-form-advanced,
    .search-results-section,
    .search-welcome,
    .latest-articles-search {
        padding: 20px 15px;
    }
    
    .search-title,
    .results-title,
    .section-title {
        font-size: 20px;
    }
    
    .search-result-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .result-image {
        width: 100%;
        height: 150px;
    }
    
    .result-title {
        font-size: 16px;
    }
    
    .search-features {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-actions {
        flex-direction: column;
    }
    
    .btn-search {
        width: 100%;
    }
    
    .welcome-content h3 {
        font-size: 24px;
    }
    
    .welcome-content i {
        font-size: 48px;
    }
    
    .no-results-message i {
        font-size: 48px;
    }
    
    .no-results-message h4 {
        font-size: 20px;
    }
}

@media (max-width: 576px) {
    .search-input-large {
        font-size: 14px;
        padding: 12px 15px;
    }
    
    .result-meta {
        gap: 10px;
        font-size: 11px;
    }
    
    .search-tags {
        justify-content: flex-start;
    }
    
    .search-tag {
        font-size: 12px;
        padding: 4px 12px;
    }
}
</style>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>

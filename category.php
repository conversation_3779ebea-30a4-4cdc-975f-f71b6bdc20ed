<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من وجود slug
if (!isset($_GET['slug']) || empty($_GET['slug'])) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$slug = sanitize($_GET['slug']);

// الحصول على الفئة
$category = getCategoryBySlug($slug);

if (!$category) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// إعدادات الترقيم
$articles_per_page = (int)getSetting('articles_per_page', 10);
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($current_page - 1) * $articles_per_page;

// الحصول على مقالات الفئة
$articles = getArticles($articles_per_page, $offset, $category['id'], 'published');

// إجمالي المقالات للترقيم
$total_articles = countArticles($category['id'], 'published');
$total_pages = ceil($total_articles / $articles_per_page);

// إعدادات الصفحة
$page_title = $category['name'] . ' - ' . getSetting('site_name', 'موقع الأخبار العربي');
$page_description = $category['description'] ?: 'تصفح جميع أخبار ' . $category['name'] . ' في ' . getSetting('site_name', 'موقع الأخبار العربي');

// تضمين الرأس
include 'includes/header.php';
?>

<div class="container">
    <!-- مسار التنقل -->
    <div class="breadcrumb-wrapper mb-4">
        <?php
        $breadcrumb_items = [
            ['title' => 'الرئيسية', 'url' => SITE_URL],
            ['title' => $category['name'], 'url' => '']
        ];
        
        echo renderBreadcrumb($breadcrumb_items);
        ?>
    </div>
    
    <div class="row">
        <!-- المحتوى الرئيسي -->
        <div class="col-lg-8 col-md-12">
            <!-- رأس الفئة -->
            <div class="category-header">
                <div class="category-info">
                    <h1 class="category-title" style="color: <?php echo $category['color']; ?>">
                        <span class="category-icon" style="background-color: <?php echo $category['color']; ?>"></span>
                        <?php echo htmlspecialchars($category['name']); ?>
                    </h1>
                    
                    <?php if ($category['description']): ?>
                    <p class="category-description">
                        <?php echo htmlspecialchars($category['description']); ?>
                    </p>
                    <?php endif; ?>
                    
                    <div class="category-stats">
                        <span class="stat-item">
                            <i class="fas fa-newspaper"></i>
                            <?php echo number_format($total_articles); ?> مقال
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-eye"></i>
                            <?php 
                            // حساب إجمالي المشاهدات للفئة
                            $db->query("SELECT SUM(views) as total_views FROM articles WHERE category_id = ? AND status = 'published'");
                            $db->bind(1, $category['id']);
                            $views_result = $db->single();
                            echo number_format($views_result['total_views'] ?: 0);
                            ?> مشاهدة
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- مقالات الفئة -->
            <?php if (!empty($articles)): ?>
            <div class="category-articles">
                <div class="articles-header">
                    <h2 class="articles-title">
                        <i class="fas fa-list"></i>
                        جميع مقالات <?php echo htmlspecialchars($category['name']); ?>
                    </h2>
                    <div class="articles-count">
                        عرض <?php echo count($articles); ?> من أصل <?php echo number_format($total_articles); ?> مقال
                    </div>
                </div>
                
                <div class="articles-grid">
                    <div class="row">
                        <?php foreach ($articles as $index => $article): ?>
                        <div class="col-lg-6 col-md-6 mb-4">
                            <div class="article-card category-article">
                                <div class="article-image">
                                    <?php 
                                    $image_url = $article['image'] ? SITE_URL . '/' . $article['image'] : SITE_URL . '/assets/images/default-article.jpg';
                                    ?>
                                    <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                        <img src="<?php echo $image_url; ?>" alt="<?php echo htmlspecialchars($article['title']); ?>" loading="lazy">
                                    </a>
                                    
                                    <?php if ($article['is_breaking']): ?>
                                    <span class="breaking-badge">عاجل</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($article['is_featured']): ?>
                                    <span class="featured-badge">مميز</span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="article-content">
                                    <h3 class="article-title">
                                        <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                            <?php echo htmlspecialchars($article['title']); ?>
                                        </a>
                                    </h3>
                                    
                                    <?php if ($article['excerpt']): ?>
                                    <p class="article-excerpt">
                                        <?php echo htmlspecialchars(truncateText($article['excerpt'], 120)); ?>
                                    </p>
                                    <?php endif; ?>
                                    
                                    <div class="article-meta">
                                        <span class="article-date">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                        </span>
                                        
                                        <?php if ($article['views'] > 0): ?>
                                        <span class="article-views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo number_format($article['views']); ?>
                                        </span>
                                        <?php endif; ?>
                                        
                                        <?php if ($article['author_name']): ?>
                                        <span class="article-author">
                                            <i class="fas fa-user"></i>
                                            <?php echo htmlspecialchars($article['author_name']); ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination-wrapper">
                    <?php 
                    $base_url = SITE_URL . '/category.php?slug=' . $category['slug'];
                    echo getPagination($current_page, $total_pages, $base_url); 
                    ?>
                </div>
                <?php endif; ?>
            </div>
            
            <?php else: ?>
            <!-- رسالة عدم وجود مقالات -->
            <div class="no-articles">
                <div class="no-content-message">
                    <i class="fas fa-newspaper"></i>
                    <h3>لا توجد مقالات في هذه الفئة حالياً</h3>
                    <p>نعمل على إضافة محتوى جديد في فئة <?php echo htmlspecialchars($category['name']); ?> قريباً</p>
                    <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- فئات أخرى -->
            <div class="other-categories mt-5">
                <h3 class="section-title">
                    <i class="fas fa-tags"></i>
                    فئات أخرى
                </h3>
                
                <div class="categories-grid">
                    <div class="row">
                        <?php 
                        $other_categories = getCategories();
                        foreach ($other_categories as $other_category):
                            if ($other_category['id'] == $category['id']) continue;
                            $category_count = countArticles($other_category['id'], 'published');
                            if ($category_count == 0) continue;
                        ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="category-card">
                                <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $other_category['slug']; ?>" 
                                   class="category-link">
                                    <div class="category-header" style="background-color: <?php echo $other_category['color']; ?>">
                                        <h4 class="category-name"><?php echo htmlspecialchars($other_category['name']); ?></h4>
                                        <span class="category-count"><?php echo $category_count; ?> مقال</span>
                                    </div>
                                    
                                    <?php if ($other_category['description']): ?>
                                    <div class="category-desc">
                                        <?php echo htmlspecialchars(truncateText($other_category['description'], 80)); ?>
                                    </div>
                                    <?php endif; ?>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4 col-md-12">
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>
</div>

<style>
/* تنسيقات صفحة الفئة */
.category-header {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    text-align: center;
}

.category-title {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.category-icon {
    width: 6px;
    height: 40px;
    border-radius: 3px;
}

.category-description {
    font-size: 16px;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-light);
    background: var(--light-color);
    padding: 8px 15px;
    border-radius: 20px;
}

.stat-item i {
    color: var(--secondary-color);
}

/* مقالات الفئة */
.category-articles {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.articles-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
    flex-wrap: wrap;
    gap: 15px;
}

.articles-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.articles-title i {
    color: var(--secondary-color);
}

.articles-count {
    font-size: 14px;
    color: var(--text-light);
    background: var(--light-color);
    padding: 6px 12px;
    border-radius: 15px;
}

/* بطاقة المقال في الفئة */
.category-article {
    height: 100%;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.category-article:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    border-color: var(--secondary-color);
}

.category-article .article-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.category-article .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.category-article:hover .article-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--warning-color);
    color: var(--white-color);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.category-article .article-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 200px);
}

.category-article .article-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
}

.category-article .article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.category-article .article-title a:hover {
    color: var(--secondary-color);
}

.category-article .article-excerpt {
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    flex: 1;
}

.category-article .article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 12px;
    color: var(--text-light);
    margin-top: auto;
}

.category-article .article-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.category-article .article-meta i {
    font-size: 11px;
}

/* رسالة عدم وجود مقالات */
.no-articles {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 60px 30px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.no-content-message i {
    font-size: 64px;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-content-message h3 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.no-content-message p {
    color: var(--text-light);
    margin-bottom: 25px;
    font-size: 16px;
}

/* الفئات الأخرى */
.other-categories {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    border: 1px solid var(--border-color);
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
}

.section-title i {
    color: var(--secondary-color);
}

.category-card {
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.category-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.category-header {
    padding: 20px;
    color: var(--white-color);
    text-align: center;
}

.category-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.category-count {
    font-size: 14px;
    opacity: 0.9;
}

.category-desc {
    padding: 15px 20px;
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
}

/* الترقيم */
.pagination-wrapper {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

/* مسار التنقل */
.breadcrumb-wrapper {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.breadcrumb {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--text-light);
}

.breadcrumb-item a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-light);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .category-header {
        padding: 20px 15px;
    }
    
    .category-title {
        font-size: 24px;
        flex-direction: column;
        gap: 10px;
    }
    
    .category-icon {
        width: 40px;
        height: 6px;
    }
    
    .category-stats {
        gap: 15px;
    }
    
    .category-articles,
    .other-categories {
        padding: 20px 15px;
    }
    
    .articles-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .articles-title {
        font-size: 20px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .category-article .article-image {
        height: 180px;
    }
    
    .no-content-message {
        padding: 40px 15px;
    }
    
    .no-content-message i {
        font-size: 48px;
    }
}

@media (max-width: 576px) {
    .category-title {
        font-size: 20px;
    }
    
    .category-description {
        font-size: 14px;
    }
    
    .stat-item {
        font-size: 12px;
        padding: 6px 12px;
    }
    
    .category-stats {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .articles-title {
        font-size: 18px;
    }
    
    .category-article .article-title {
        font-size: 16px;
    }
    
    .category-article .article-meta {
        gap: 10px;
        font-size: 11px;
    }
    
    .breadcrumb {
        flex-wrap: wrap;
        gap: 5px;
    }
}
</style>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>

<?php
// ملف تهيئة قاعدة البيانات والبيانات التجريبية

// تضمين ملف الإعدادات
require_once dirname(__DIR__) . '/config/config.php';

// التحقق من وجود قاعدة البيانات
try {
    $db = new Database();
    
    echo "<h2>بدء تهيئة قاعدة البيانات...</h2>";
    
    // إنشاء قاعدة البيانات
    if ($db->createDatabase()) {
        echo "<p>✓ تم إنشاء قاعدة البيانات بنجاح</p>";
    } else {
        echo "<p>✗ فشل في إنشاء قاعدة البيانات</p>";
        exit;
    }
    
    // إنشاء الجداول
    createTables($db);
    
    // إدراج البيانات التجريبية
    insertSampleData($db);
    
    echo "<h2>تم الانتهاء من تهيئة قاعدة البيانات بنجاح!</h2>";
    echo "<p><a href='" . SITE_URL . "'>عرض الموقع</a> | <a href='" . SITE_URL . "/admin/'>لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p>خطأ: " . $e->getMessage() . "</p>";
}

function createTables($db) {
    echo "<h3>إنشاء الجداول...</h3>";
    
    // جدول المستخدمين
    $db->query("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('admin', 'editor', 'author') DEFAULT 'author',
        status ENUM('active', 'inactive') DEFAULT 'active',
        avatar VARCHAR(255) NULL,
        bio TEXT NULL,
        last_login DATETIME NULL,
        last_activity DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول المستخدمين</p>";
    
    // جدول الفئات
    $db->query("CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT NULL,
        color VARCHAR(7) DEFAULT '#3498db',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول الفئات</p>";
    
    // جدول المقالات
    $db->query("CREATE TABLE IF NOT EXISTS articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        excerpt TEXT NULL,
        content LONGTEXT NOT NULL,
        image VARCHAR(255) NULL,
        category_id INT NULL,
        author_id INT NOT NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        is_featured BOOLEAN DEFAULT FALSE,
        is_breaking BOOLEAN DEFAULT FALSE,
        views INT DEFAULT 0,
        source_url VARCHAR(500) NULL,
        rss_source_id INT NULL,
        published_at DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_status (status),
        INDEX idx_published_at (published_at),
        INDEX idx_category (category_id),
        INDEX idx_featured (is_featured),
        INDEX idx_breaking (is_breaking)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول المقالات</p>";
    
    // جدول التعليقات
    $db->query("CREATE TABLE IF NOT EXISTS comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        article_id INT NOT NULL,
        parent_id INT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        content TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (article_id) REFERENCES articles(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
        INDEX idx_article (article_id),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول التعليقات</p>";
    
    // جدول مصادر RSS
    $db->query("CREATE TABLE IF NOT EXISTS rss_sources (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        url VARCHAR(500) NOT NULL,
        category_id INT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        fetch_interval INT DEFAULT 60,
        last_fetch DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول مصادر RSS</p>";
    
    // جدول الإعدادات
    $db->query("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NULL,
        setting_type ENUM('text', 'textarea', 'number', 'boolean', 'file') DEFAULT 'text',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول الإعدادات</p>";
    
    // جدول جدولة المهام
    $db->query("CREATE TABLE IF NOT EXISTS scheduled_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_name VARCHAR(100) NOT NULL,
        task_type ENUM('rss_fetch', 'cleanup', 'backup') NOT NULL,
        parameters JSON NULL,
        status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
        scheduled_at DATETIME NOT NULL,
        started_at DATETIME NULL,
        completed_at DATETIME NULL,
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    $db->execute();
    echo "<p>✓ جدول جدولة المهام</p>";
}

function insertSampleData($db) {
    echo "<h3>إدراج البيانات التجريبية...</h3>";
    
    // إدراج المستخدم الإداري
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $db->query("INSERT IGNORE INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)");
    $db->bind(1, 'admin');
    $db->bind(2, '<EMAIL>');
    $db->bind(3, $admin_password);
    $db->bind(4, 'المدير العام');
    $db->bind(5, 'admin');
    $db->bind(6, 'active');
    $db->execute();
    echo "<p>✓ تم إنشاء المستخدم الإداري (admin / admin123)</p>";
    
    // إدراج محرر
    $editor_password = password_hash('editor123', PASSWORD_DEFAULT);
    $db->query("INSERT IGNORE INTO users (username, email, password, full_name, role, status) VALUES (?, ?, ?, ?, ?, ?)");
    $db->bind(1, 'editor');
    $db->bind(2, '<EMAIL>');
    $db->bind(3, $editor_password);
    $db->bind(4, 'محرر الأخبار');
    $db->bind(5, 'editor');
    $db->bind(6, 'active');
    $db->execute();
    echo "<p>✓ تم إنشاء المحرر (editor / editor123)</p>";
    
    // إدراج الفئات
    $categories = [
        ['أخبار محلية', 'local-news', 'آخر الأخبار المحلية والداخلية', '#e74c3c'],
        ['أخبار عالمية', 'world-news', 'أخبار من حول العالم', '#3498db'],
        ['سياسة', 'politics', 'الأخبار السياسية والتحليلات', '#9b59b6'],
        ['اقتصاد', 'economy', 'الأخبار الاقتصادية والمالية', '#27ae60'],
        ['رياضة', 'sports', 'أخبار الرياضة والألعاب', '#f39c12'],
        ['تكنولوجيا', 'technology', 'أخبار التكنولوجيا والابتكار', '#1abc9c'],
        ['صحة', 'health', 'أخبار الصحة والطب', '#e67e22'],
        ['ثقافة وفن', 'culture', 'الأخبار الثقافية والفنية', '#34495e']
    ];
    
    foreach ($categories as $category) {
        $db->query("INSERT IGNORE INTO categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
        $db->bind(1, $category[0]);
        $db->bind(2, $category[1]);
        $db->bind(3, $category[2]);
        $db->bind(4, $category[3]);
        $db->execute();
    }
    echo "<p>✓ تم إدراج الفئات</p>";
    
    // إدراج مقالات تجريبية
    $articles = [
        [
            'عنوان مقال تجريبي أول',
            'sample-article-1',
            'هذا مقتطف من المقال التجريبي الأول يوضح محتوى المقال بشكل مختصر.',
            'هذا محتوى المقال التجريبي الأول. يحتوي على نص تجريبي لإظهار كيفية عرض المقالات في الموقع. يمكن أن يحتوي المقال على فقرات متعددة ونصوص طويلة لاختبار التصميم والتنسيق.',
            1, 1, 'published', 1, 0
        ],
        [
            'مقال إخباري عاجل',
            'breaking-news-article',
            'خبر عاجل ومهم يتطلب انتباه القراء فوراً.',
            'هذا مقال إخباري عاجل يحتوي على معلومات مهمة وحديثة. يتم عرض هذا النوع من المقالات بشكل بارز في الموقع لجذب انتباه القراء إلى الأخبار المهمة والعاجلة.',
            1, 1, 'published', 0, 1
        ],
        [
            'تطورات تكنولوجية حديثة',
            'tech-developments',
            'آخر التطورات في عالم التكنولوجيا والابتكار.',
            'مقال يتناول أحدث التطورات التكنولوجية والابتكارات في مختلف المجالات. يشمل المقال معلومات عن الذكاء الاصطناعي، إنترنت الأشياء، والتقنيات الناشئة.',
            6, 1, 'published', 1, 0
        ],
        [
            'تحليل اقتصادي شامل',
            'economic-analysis',
            'تحليل شامل للوضع الاقتصادي الحالي والتوقعات المستقبلية.',
            'مقال تحليلي يتناول الوضع الاقتصادي الحالي مع توقعات للمستقبل. يشمل التحليل مؤشرات اقتصادية مهمة وتأثيرها على الأسواق المحلية والعالمية.',
            4, 1, 'published', 0, 0
        ],
        [
            'أخبار رياضية متنوعة',
            'sports-news',
            'تغطية شاملة لأهم الأحداث الرياضية المحلية والعالمية.',
            'مقال رياضي يغطي أهم الأحداث والمباريات في مختلف الألعاب الرياضية. يتضمن نتائج المباريات، تحليلات فنية، وأخبار اللاعبين والفرق.',
            5, 1, 'published', 0, 0
        ]
    ];
    
    foreach ($articles as $article) {
        $db->query("INSERT IGNORE INTO articles (title, slug, excerpt, content, category_id, author_id, status, is_featured, is_breaking, published_at, views) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)");
        $db->bind(1, $article[0]);
        $db->bind(2, $article[1]);
        $db->bind(3, $article[2]);
        $db->bind(4, $article[3]);
        $db->bind(5, $article[4]);
        $db->bind(6, $article[5]);
        $db->bind(7, $article[6]);
        $db->bind(8, $article[7]);
        $db->bind(9, $article[8]);
        $db->bind(10, rand(50, 500));
        $db->execute();
    }
    echo "<p>✓ تم إدراج المقالات التجريبية</p>";
    
    // إدراج تعليقات تجريبية
    $comments = [
        [1, 'أحمد محمد', '<EMAIL>', 'مقال رائع ومفيد جداً، شكراً لكم على هذا المحتوى المميز.'],
        [1, 'فاطمة علي', '<EMAIL>', 'معلومات قيمة، أتطلع لقراءة المزيد من هذه المقالات.'],
        [2, 'محمد سالم', '<EMAIL>', 'خبر مهم، شكراً على التغطية السريعة.'],
        [3, 'سارة أحمد', '<EMAIL>', 'موضوع شيق حول التكنولوجيا، استفدت كثيراً.'],
        [4, 'عبدالله خالد', '<EMAIL>', 'تحليل ممتاز للوضع الاقتصادي، مفيد جداً.']
    ];
    
    foreach ($comments as $comment) {
        $db->query("INSERT INTO comments (article_id, name, email, content, status, ip_address) VALUES (?, ?, ?, ?, 'approved', '127.0.0.1')");
        $db->bind(1, $comment[0]);
        $db->bind(2, $comment[1]);
        $db->bind(3, $comment[2]);
        $db->bind(4, $comment[3]);
        $db->execute();
    }
    echo "<p>✓ تم إدراج التعليقات التجريبية</p>";
    
    // إدراج الإعدادات الأساسية
    $settings = [
        ['site_name', 'موقع الأخبار العربي', 'text'],
        ['site_description', 'موقع إخباري عربي شامل يقدم آخر الأخبار والتحليلات', 'textarea'],
        ['site_keywords', 'أخبار، عربي، سياسة، اقتصاد، رياضة، تكنولوجيا', 'text'],
        ['articles_per_page', '10', 'number'],
        ['comments_enabled', '1', 'boolean'],
        ['breaking_news_enabled', '1', 'boolean'],
        ['rss_auto_fetch', '1', 'boolean'],
        ['google_analytics_id', '', 'text'],
        ['facebook_url', '', 'text'],
        ['twitter_url', '', 'text'],
        ['instagram_url', '', 'text'],
        ['youtube_url', '', 'text']
    ];
    
    foreach ($settings as $setting) {
        $db->query("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
        $db->bind(1, $setting[0]);
        $db->bind(2, $setting[1]);
        $db->bind(3, $setting[2]);
        $db->execute();
    }
    echo "<p>✓ تم إدراج الإعدادات الأساسية</p>";
    
    // إدراج مصادر RSS تجريبية
    $rss_sources = [
        ['الجزيرة نت', 'https://www.aljazeera.net/rss/all', 1],
        ['بي بي سي عربي', 'https://feeds.bbci.co.uk/arabic/rss.xml', 2],
        ['العربية نت', 'https://www.alarabiya.net/rss', 1]
    ];
    
    foreach ($rss_sources as $source) {
        $db->query("INSERT IGNORE INTO rss_sources (name, url, category_id, is_active) VALUES (?, ?, ?, 1)");
        $db->bind(1, $source[0]);
        $db->bind(2, $source[1]);
        $db->bind(3, $source[2]);
        $db->execute();
    }
    echo "<p>✓ تم إدراج مصادر RSS التجريبية</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تهيئة قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        p {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
            border-right: 4px solid #27ae60;
        }
        a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

        </div>
    </main>
    
    <!-- تذييل لوحة التحكم -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-left">
                <p>&copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name', 'موقع الأخبار العربي'); ?>. جميع الحقوق محفوظة.</p>
            </div>
            <div class="footer-right">
                <span class="footer-version">الإصدار 1.0</span>
                <span class="footer-separator">|</span>
                <a href="<?php echo SITE_URL; ?>" target="_blank" class="footer-link">عرض الموقع</a>
                <span class="footer-separator">|</span>
                <a href="#" class="footer-link">المساعدة</a>
            </div>
        </div>
    </footer>
    
    <!-- نافذة منبثقة للرسائل -->
    <div id="admin-message-container"></div>
    
    <!-- نافذة تأكيد الحذف -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">تأكيد الحذف</h4>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <p id="deleteMessage">هل أنت متأكد من أنك تريد حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
    
    <!-- نافذة التحميل -->
    <div id="loadingModal" class="modal loading-modal">
        <div class="loading-content">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p id="loadingMessage">جاري التحميل...</p>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>
    <script src="<?php echo SITE_URL; ?>/admin/assets/js/admin.js"></script>
    
    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

<style>
/* تنسيقات تذييل لوحة التحكم */
.admin-footer {
    background: var(--white-color);
    border-top: 1px solid var(--border-color);
    padding: 15px 30px;
    margin-top: auto;
    margin-right: 280px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: var(--text-light);
}

.footer-left p {
    margin: 0;
}

.footer-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-separator {
    color: var(--border-color);
}

.footer-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-link:hover {
    color: var(--primary-color);
}

.footer-version {
    background: var(--light-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
}

/* نافذة منبثقة للرسائل */
#admin-message-container {
    position: fixed;
    top: 90px;
    left: 20px;
    z-index: 9999;
    max-width: 400px;
}

.admin-message {
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: var(--shadow-hover);
    display: flex;
    align-items: center;
    gap: 12px;
    transform: translateX(-100%);
    transition: var(--transition);
}

.admin-message.show {
    transform: translateX(0);
}

.admin-message.success {
    border-right: 4px solid var(--success-color);
}

.admin-message.error {
    border-right: 4px solid var(--accent-color);
}

.admin-message.warning {
    border-right: 4px solid var(--warning-color);
}

.admin-message.info {
    border-right: 4px solid var(--secondary-color);
}

.admin-message i {
    font-size: 18px;
}

.admin-message.success i {
    color: var(--success-color);
}

.admin-message.error i {
    color: var(--accent-color);
}

.admin-message.warning i {
    color: var(--warning-color);
}

.admin-message.info i {
    color: var(--secondary-color);
}

.admin-message-content {
    flex: 1;
}

.admin-message-title {
    font-weight: 600;
    margin-bottom: 3px;
    font-size: 14px;
}

.admin-message-text {
    font-size: 13px;
    color: var(--text-light);
    line-height: 1.4;
}

.admin-message-close {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    transition: var(--transition);
}

.admin-message-close:hover {
    color: var(--text-color);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition);
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 25px;
    text-align: center;
}

.delete-icon {
    margin-bottom: 20px;
}

.delete-icon i {
    font-size: 48px;
    color: var(--warning-color);
}

.modal-body p {
    font-size: 16px;
    color: var(--text-color);
    line-height: 1.5;
    margin: 0;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
}

/* نافذة التحميل */
.loading-modal {
    background: rgba(0,0,0,0.7);
}

.loading-content {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 40px;
    text-align: center;
    max-width: 300px;
}

.loading-spinner {
    margin-bottom: 20px;
}

.loading-spinner i {
    font-size: 32px;
    color: var(--secondary-color);
}

.loading-content p {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

/* أزرار إضافية */
.btn-orange {
    background: #ff9500;
    color: var(--white-color);
    border: 1px solid #ff9500;
}

.btn-orange:hover {
    background: #e6850e;
    border-color: #e6850e;
}

.btn-outline-orange {
    background: transparent;
    color: #ff9500;
    border: 1px solid #ff9500;
}

.btn-outline-orange:hover {
    background: #ff9500;
    color: var(--white-color);
}

/* التصميم المتجاوب للتذييل */
@media (max-width: 768px) {
    .admin-footer {
        margin-right: 0;
        padding: 15px 20px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .footer-right {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    #admin-message-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px 20px;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .modal-footer .btn {
        width: 100%;
    }
}

/* تحسينات إضافية */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

/* تحسينات إمكانية الوصول */
.modal:focus {
    outline: none;
}

.modal-close:focus,
.admin-message-close:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* طباعة */
@media print {
    .admin-footer,
    #admin-message-container,
    .modal {
        display: none;
    }
}
</style>

<script>
// وظائف JavaScript إضافية للوحة التحكم

// عرض رسالة في لوحة التحكم
function showAdminMessage(message, type = 'info', title = '', duration = 5000) {
    const container = document.getElementById('admin-message-container');
    const messageId = 'msg-' + Date.now();
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    const titles = {
        success: title || 'نجح',
        error: title || 'خطأ',
        warning: title || 'تحذير',
        info: title || 'معلومات'
    };
    
    const messageElement = document.createElement('div');
    messageElement.id = messageId;
    messageElement.className = `admin-message ${type}`;
    messageElement.innerHTML = `
        <i class="${icons[type]}"></i>
        <div class="admin-message-content">
            <div class="admin-message-title">${titles[type]}</div>
            <div class="admin-message-text">${message}</div>
        </div>
        <button class="admin-message-close" onclick="closeAdminMessage('${messageId}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    container.appendChild(messageElement);
    
    // إظهار الرسالة
    setTimeout(() => {
        messageElement.classList.add('show');
    }, 100);
    
    // إخفاء الرسالة تلقائياً
    if (duration > 0) {
        setTimeout(() => {
            closeAdminMessage(messageId);
        }, duration);
    }
    
    return messageId;
}

// إغلاق رسالة
function closeAdminMessage(messageId) {
    const message = document.getElementById(messageId);
    if (message) {
        message.classList.remove('show');
        setTimeout(() => {
            message.remove();
        }, 300);
    }
}

// نافذة تأكيد الحذف
function showDeleteModal(message, onConfirm) {
    const modal = document.getElementById('deleteModal');
    const messageElement = document.getElementById('deleteMessage');
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    
    messageElement.textContent = message || 'هل أنت متأكد من أنك تريد حذف هذا العنصر؟';
    
    // إزالة مستمعي الأحداث السابقين
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // إضافة مستمع الحدث الجديد
    newConfirmBtn.addEventListener('click', function() {
        closeDeleteModal();
        if (typeof onConfirm === 'function') {
            onConfirm();
        }
    });
    
    modal.classList.add('show');
}

// إغلاق نافذة تأكيد الحذف
function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('show');
}

// نافذة التحميل
function showLoadingModal(message = 'جاري التحميل...') {
    const modal = document.getElementById('loadingModal');
    const messageElement = document.getElementById('loadingMessage');
    
    messageElement.textContent = message;
    modal.classList.add('show');
}

// إخفاء نافذة التحميل
function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    modal.classList.remove('show');
}

// إغلاق النوافذ المنبثقة بالضغط على Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDeleteModal();
        hideLoadingModal();
    }
});

// إغلاق النوافذ المنبثقة بالنقر خارجها
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        if (e.target.id === 'deleteModal') {
            closeDeleteModal();
        } else if (e.target.id === 'loadingModal') {
            hideLoadingModal();
        }
    }
});

// تأكيد قبل مغادرة الصفحة إذا كان هناك تغييرات غير محفوظة
let hasUnsavedChanges = false;

function setUnsavedChanges(value) {
    hasUnsavedChanges = value;
}

window.addEventListener('beforeunload', function(e) {
    if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = 'لديك تغييرات غير محفوظة. هل أنت متأكد من أنك تريد المغادرة؟';
        return e.returnValue;
    }
});

// مراقبة التغييرات في النماذج
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                setUnsavedChanges(true);
            });
        });
        
        form.addEventListener('submit', function() {
            setUnsavedChanges(false);
        });
    });
});

// وظائف AJAX مساعدة
function makeAjaxRequest(url, method = 'GET', data = null, onSuccess = null, onError = null) {
    const xhr = new XMLHttpRequest();
    
    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (typeof onSuccess === 'function') {
                        onSuccess(response);
                    }
                } catch (e) {
                    if (typeof onError === 'function') {
                        onError('خطأ في تحليل الاستجابة');
                    }
                }
            } else {
                if (typeof onError === 'function') {
                    onError('خطأ في الشبكة: ' + xhr.status);
                }
            }
        }
    };
    
    if (data) {
        xhr.send(JSON.stringify(data));
    } else {
        xhr.send();
    }
}

// تحديث الوقت بشكل دوري
function updateTimeElements() {
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-time');
        if (timestamp) {
            element.textContent = timeAgo(timestamp);
        }
    });
}

// تحديث كل دقيقة
setInterval(updateTimeElements, 60000);

// وظيفة timeAgo للجافاسكريبت
function timeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'منذ لحظات';
    if (diffInSeconds < 3600) return 'منذ ' + Math.floor(diffInSeconds / 60) + ' دقيقة';
    if (diffInSeconds < 86400) return 'منذ ' + Math.floor(diffInSeconds / 3600) + ' ساعة';
    if (diffInSeconds < 2592000) return 'منذ ' + Math.floor(diffInSeconds / 86400) + ' يوم';
    if (diffInSeconds < 31536000) return 'منذ ' + Math.floor(diffInSeconds / 2592000) + ' شهر';
    return 'منذ ' + Math.floor(diffInSeconds / 31536000) + ' سنة';
}

// تهيئة التلميحات
document.addEventListener('DOMContentLoaded', function() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            // يمكن إضافة مكتبة tooltip هنا
        });
    });
});

console.log('تم تحميل لوحة التحكم بنجاح');
</script>

</body>
</html>

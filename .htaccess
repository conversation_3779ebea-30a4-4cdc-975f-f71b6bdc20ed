# ملف .htaccess للموقع الإخباري العربي

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول لمجلدات النظام
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/includes/
RedirectMatch 403 ^/install/

# إعادة توجيه الروابط الودية
# مقال واحد: /article/slug
RewriteRule ^article/([a-zA-Z0-9\-_]+)/?$ article.php?slug=$1 [L,QSA]

# فئة: /category/slug
RewriteRule ^category/([a-zA-Z0-9\-_]+)/?$ category.php?slug=$1 [L,QSA]

# البحث: /search/query
RewriteRule ^search/([^/]+)/?$ search.php?q=$1 [L,QSA]

# صفحة المؤلف: /author/id
RewriteRule ^author/([0-9]+)/?$ author.php?id=$1 [L,QSA]

# أرشيف التاريخ: /archive/year/month
RewriteRule ^archive/([0-9]{4})/([0-9]{1,2})/?$ archive.php?year=$1&month=$2 [L,QSA]

# RSS Feed: /rss
RewriteRule ^rss/?$ rss.php [L]

# Sitemap: /sitemap.xml
RewriteRule ^sitemap\.xml$ sitemap.php [L]

# إعادة توجيه الصفحات القديمة (إذا كانت موجودة)
# RewriteRule ^old-page/?$ new-page.php [R=301,L]

# إعادة توجيه 404 للصفحات غير الموجودة
ErrorDocument 404 /404.php

# تحسين الأداء والأمان

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # XML و JSON
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
</IfModule>

# إضافة رؤوس الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # تفعيل حماية XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # سياسة الأمان للمحتوى (CSP)
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-src 'self' https://www.youtube.com; object-src 'none';"
    
    # HSTS (إذا كان الموقع يستخدم HTTPS)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
</IfModule>

# منع الوصول للملفات المخفية
<Files ".*">
    Order Allow,Deny
    Deny from all
</Files>

# السماح بالوصول لـ .well-known (مطلوب لـ SSL)
<Files ".well-known">
    Order Allow,Deny
    Allow from all
</Files>

# منع تنفيذ PHP في مجلد الرفع
<Directory "uploads">
    php_flag engine off
    AddType text/plain .php .php3 .phtml .pht
</Directory>

# منع الوصول المباشر للملفات في مجلد الأصول
<Directory "assets">
    Options -Indexes
</Directory>

# تحسين الأداء
<IfModule mod_rewrite.c>
    # إزالة www (اختياري)
    # RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    # RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # إجبار HTTPS (اختياري)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إضافة شرطة مائلة في النهاية للمجلدات
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_URI} !(.*)/$
    RewriteRule ^(.*)$ $1/ [L,R=301]
</IfModule>

# منع الربط المباشر للصور (Hotlinking)
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yourdomain\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [F]
</IfModule>

# تحديد حجم الرفع الأقصى
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# تفعيل ضغط الإخراج
php_flag zlib.output_compression On

# إخفاء أخطاء PHP في الإنتاج
php_flag display_errors Off
php_flag log_errors On

# تحسين الذاكرة
php_value memory_limit 256M

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول لملفات قواعد البيانات
<FilesMatch "\.(sql|db|sqlite)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين SEO - إعادة توجيه الصفحات المكررة
RewriteCond %{THE_REQUEST} /+[^?]+?/+(\?.*)?(\s|\?|&|$) [NC]
RewriteRule ^(.+)$ /%1 [R=301,L]

# منع الوصول لملفات التكوين
<FilesMatch "^(config|settings|database)\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# تحسين الأمان - منع تنفيذ السكريبت في مجلدات معينة
<Directory "uploads">
    <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
        Order Allow,Deny
        Deny from all
    </FilesMatch>
</Directory>

# إعدادات خاصة بـ Apache 2.4+
<IfModule mod_authz_core.c>
    <Files "config.php">
        Require all denied
    </Files>
    
    <Files "*.log">
        Require all denied
    </Files>
    
    <Directory "config">
        Require all denied
    </Directory>
</IfModule>

# تحسين الفهرسة لمحركات البحث
<IfModule mod_headers.c>
    # إضافة معلومات اللغة
    Header set Content-Language "ar"
    
    # تحسين التخزين المؤقت للمتصفح
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
</IfModule>

# منع عرض قائمة الملفات
Options -Indexes

# تحسين الأداء - تقليل عدد طلبات HTTP
<IfModule mod_expires.c>
    # تفعيل انتهاء الصلاحية
    ExpiresActive on
    
    # انتهاء صلاحية افتراضي
    ExpiresDefault "access plus 1 month"
    
    # انتهاء صلاحية خاص بـ HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # انتهاء صلاحية خاص بـ RSS
    ExpiresByType application/rss+xml "access plus 1 hour"
</IfModule>

# إعادة توجيه خاصة للصفحات المهمة
# RewriteRule ^admin$ admin/ [R=301,L]
# RewriteRule ^rss$ rss.php [R=301,L]

# تحسين الأمان - منع الوصول للملفات الحساسة
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# نهاية ملف .htaccess

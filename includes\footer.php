    </main>
    <!-- نهاية المحتوى الرئيسي -->
    
    <!-- تذييل الصفحة -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- معلومات الموقع -->
                <div class="footer-section">
                    <h3>
                        <i class="fas fa-newspaper"></i>
                        <?php echo getSetting('site_name', 'موقع الأخبار العربي'); ?>
                    </h3>
                    <p><?php echo getSetting('site_description', 'موقع إخباري عربي شامل يقدم آخر الأخبار والتحليلات من جميع أنحاء العالم العربي والعالم.'); ?></p>
                    <div class="footer-social">
                        <a href="#" class="social-link" title="فيسبوك">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link" title="تويتر">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" title="إنستغرام">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link" title="يوتيوب">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="#" class="social-link" title="تيليجرام">
                            <i class="fab fa-telegram"></i>
                        </a>
                        <a href="#" class="social-link" title="واتساب">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
                
                <!-- روابط سريعة -->
                <div class="footer-section">
                    <h3>
                        <i class="fas fa-link"></i>
                        روابط سريعة
                    </h3>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                        <?php 
                        $footer_categories = getCategories();
                        foreach (array_slice($footer_categories, 0, 6) as $category): 
                        ?>
                        <li><a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>"><?php echo htmlspecialchars($category['name']); ?></a></li>
                        <?php endforeach; ?>
                        <li><a href="<?php echo SITE_URL; ?>/search.php">البحث</a></li>
                    </ul>
                </div>
                
                <!-- أحدث المقالات -->
                <div class="footer-section">
                    <h3>
                        <i class="fas fa-clock"></i>
                        أحدث المقالات
                    </h3>
                    <?php 
                    $recent_articles = getArticles(5, 0, null, 'published');
                    ?>
                    <ul class="recent-articles">
                        <?php foreach ($recent_articles as $article): ?>
                        <li>
                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                <div class="recent-article-title"><?php echo htmlspecialchars(truncateText($article['title'], 60)); ?></div>
                                <div class="recent-article-date"><?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?></div>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- معلومات الاتصال -->
                <div class="footer-section">
                    <h3>
                        <i class="fas fa-envelope"></i>
                        تواصل معنا
                    </h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <a href="tel:+966123456789">+966 12 345 6789</a>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>الرياض، المملكة العربية السعودية</span>
                        </div>
                    </div>
                    
                    <!-- نشرة إخبارية -->
                    <div class="newsletter">
                        <h4>اشترك في النشرة الإخبارية</h4>
                        <form class="newsletter-form" action="<?php echo SITE_URL; ?>/subscribe.php" method="POST">
                            <div class="newsletter-input-group">
                                <input type="email" name="email" placeholder="بريدك الإلكتروني" required>
                                <button type="submit" class="newsletter-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- الشريط السفلي -->
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            &copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name', 'موقع الأخبار العربي'); ?>. 
                            جميع الحقوق محفوظة.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <ul class="footer-links">
                            <li><a href="<?php echo SITE_URL; ?>/privacy.php">سياسة الخصوصية</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/terms.php">شروط الاستخدام</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/contact.php">اتصل بنا</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/rss.php">RSS</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- زر العودة للأعلى -->
    <button class="scroll-to-top" title="العودة للأعلى">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- نافذة منبثقة للرسائل -->
    <div id="message-container"></div>
    
    <!-- JavaScript Files -->
    <script src="<?php echo SITE_URL; ?>/assets/js/main.js"></script>
    
    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Google Analytics (إضافي) -->
    <?php if (getSetting('google_analytics_id')): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo getSetting('google_analytics_id'); ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo getSetting('google_analytics_id'); ?>');
    </script>
    <?php endif; ?>

<style>
/* تنسيقات التذييل */
.footer {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    color: var(--white-color);
    padding: 50px 0 0;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-section p {
    line-height: 1.7;
    color: rgba(255,255,255,0.8);
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-section a:hover {
    color: var(--secondary-color);
    transform: translateX(-5px);
}

.footer-social {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.footer-social .social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    color: var(--white-color);
    border-radius: 50%;
    transition: var(--transition);
    font-size: 16px;
}

.footer-social .social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

.recent-articles li {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.recent-articles li:last-child {
    border-bottom: none;
}

.recent-article-title {
    font-weight: 500;
    margin-bottom: 5px;
    line-height: 1.4;
}

.recent-article-date {
    font-size: 12px;
    color: rgba(255,255,255,0.6);
}

.contact-info {
    margin-bottom: 25px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    color: rgba(255,255,255,0.8);
}

.contact-item i {
    width: 20px;
    color: var(--secondary-color);
}

.newsletter h4 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-size: 16px;
}

.newsletter-input-group {
    display: flex;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.newsletter-input-group input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    background: rgba(255,255,255,0.1);
    color: var(--white-color);
    font-size: 14px;
}

.newsletter-input-group input::placeholder {
    color: rgba(255,255,255,0.6);
}

.newsletter-input-group input:focus {
    outline: none;
    background: rgba(255,255,255,0.2);
}

.newsletter-btn {
    padding: 12px 15px;
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter-btn:hover {
    background: #2980b9;
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding: 25px 0;
    margin-top: 40px;
}

.copyright {
    margin: 0;
    color: rgba(255,255,255,0.7);
    font-size: 14px;
}

.footer-links {
    display: flex;
    justify-content: flex-end;
    gap: 20px;
    list-style: none;
    margin: 0;
}

.footer-links a {
    color: rgba(255,255,255,0.7);
    font-size: 14px;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--secondary-color);
}

/* زر العودة للأعلى */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: #2980b9;
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

/* نافذة الرسائل المنبثقة */
.message-popup {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    z-index: 9999;
    transform: translateX(100%);
    transition: var(--transition);
}

.message-popup.show {
    transform: translateX(0);
}

.message-popup.alert-success {
    background: var(--success-color);
    color: var(--white-color);
}

.message-popup.alert-error {
    background: var(--accent-color);
    color: var(--white-color);
}

.message-popup.alert-info {
    background: var(--secondary-color);
    color: var(--white-color);
}

.message-popup.alert-warning {
    background: var(--warning-color);
    color: var(--white-color);
}

/* الوضع الليلي */
.dark-mode {
    --primary-color: #1a1a1a;
    --secondary-color: #4a90e2;
    --text-color: #e0e0e0;
    --text-light: #b0b0b0;
    --white-color: #2d2d2d;
    --light-color: #3a3a3a;
    --border-color: #4a4a4a;
    background-color: #121212;
}

.dark-mode .header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

.dark-mode .navbar {
    background: #2d2d2d;
    border-bottom: 1px solid #4a4a4a;
}

.dark-mode .article-card {
    background: #2d2d2d;
    border: 1px solid #4a4a4a;
}

.dark-mode .sidebar-widget {
    background: #2d2d2d;
    border: 1px solid #4a4a4a;
}

.dark-mode .search-results {
    background: #2d2d2d;
    border: 1px solid #4a4a4a;
}

.dark-mode .dropdown-menu {
    background: #2d2d2d;
    border: 1px solid #4a4a4a;
}

/* التصميم المتجاوب للتذييل */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .scroll-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
    
    .message-popup {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .newsletter-input-group {
        flex-direction: column;
    }
    
    .newsletter-input-group input {
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .newsletter-btn {
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 30px 0 0;
    }
    
    .footer-section h3 {
        font-size: 16px;
    }
    
    .footer-social .social-link {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}
</style>

</body>
</html>

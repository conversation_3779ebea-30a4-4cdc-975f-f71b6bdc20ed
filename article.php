<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// التحقق من وجود slug
if (!isset($_GET['slug']) || empty($_GET['slug'])) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

$slug = sanitize($_GET['slug']);

// الحصول على المقال
$article = getArticleBySlug($slug);

if (!$article) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// زيادة عدد المشاهدات
incrementArticleViews($article['id']);

// إعدادات الصفحة
$page_title = $article['title'];
$page_description = $article['excerpt'] ?: truncateText(strip_tags($article['content']), 160);
$page_image = $article['image'] ? $article['image'] : '';
$page_url = SITE_URL . '/article.php?slug=' . $article['slug'];

// الحصول على المقالات ذات الصلة
$related_articles = getRelatedArticles($article['id'], $article['category_id'], 4);

// الحصول على التعليقات
$comments = getComments($article['id'], 'approved');
$comments_count = countComments($article['id'], 'approved');

// معالجة إرسال التعليق
$comment_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_comment'])) {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $content = sanitize($_POST['content'] ?? '');
    
    if (!empty($name) && !empty($email) && !empty($content)) {
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $ip_address = $_SERVER['REMOTE_ADDR'];
            
            if (addComment($article['id'], $name, $email, $content, $ip_address)) {
                $comment_message = '<div class="alert alert-success">تم إرسال تعليقك بنجاح وسيتم مراجعته قريباً.</div>';
            } else {
                $comment_message = '<div class="alert alert-danger">حدث خطأ أثناء إرسال التعليق. يرجى المحاولة مرة أخرى.</div>';
            }
        } else {
            $comment_message = '<div class="alert alert-danger">يرجى إدخال بريد إلكتروني صحيح.</div>';
        }
    } else {
        $comment_message = '<div class="alert alert-danger">يرجى ملء جميع الحقول المطلوبة.</div>';
    }
}

// تضمين الرأس
include 'includes/header.php';
?>

<div class="container">
    <!-- مسار التنقل -->
    <div class="breadcrumb-wrapper mb-4">
        <?php
        $breadcrumb_items = [
            ['title' => 'الرئيسية', 'url' => SITE_URL],
        ];
        
        if ($article['category_name']) {
            $breadcrumb_items[] = [
                'title' => $article['category_name'],
                'url' => SITE_URL . '/category.php?slug=' . $article['category_slug']
            ];
        }
        
        $breadcrumb_items[] = ['title' => $article['title'], 'url' => ''];
        
        echo renderBreadcrumb($breadcrumb_items);
        ?>
    </div>
    
    <div class="row">
        <!-- محتوى المقال -->
        <div class="col-lg-8 col-md-12">
            <article class="article-detail">
                <!-- رأس المقال -->
                <header class="article-header">
                    <?php if ($article['category_name']): ?>
                    <div class="article-category">
                        <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $article['category_slug']; ?>" 
                           class="category-link" style="background-color: <?php echo $article['category_color']; ?>">
                            <?php echo htmlspecialchars($article['category_name']); ?>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <h1 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h1>
                    
                    <?php if ($article['excerpt']): ?>
                    <div class="article-excerpt">
                        <?php echo htmlspecialchars($article['excerpt']); ?>
                    </div>
                    <?php endif; ?>
                    
                    <div class="article-meta">
                        <div class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span><?php echo formatDate($article['published_at'] ?: $article['created_at'], 'd F Y - H:i'); ?></span>
                        </div>
                        
                        <?php if ($article['author_name']): ?>
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span><?php echo htmlspecialchars($article['author_name']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo number_format($article['views']); ?> مشاهدة</span>
                        </div>
                        
                        <?php if ($comments_count > 0): ?>
                        <div class="meta-item">
                            <i class="fas fa-comments"></i>
                            <span><?php echo $comments_count; ?> تعليق</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- أزرار المشاركة -->
                    <div class="article-share">
                        <span class="share-label">شارك المقال:</span>
                        <div class="share-buttons">
                            <button onclick="shareArticle('facebook', '<?php echo $page_url; ?>', '<?php echo addslashes($article['title']); ?>')" 
                                    class="share-btn facebook" title="مشاركة على فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </button>
                            <button onclick="shareArticle('twitter', '<?php echo $page_url; ?>', '<?php echo addslashes($article['title']); ?>')" 
                                    class="share-btn twitter" title="مشاركة على تويتر">
                                <i class="fab fa-twitter"></i>
                            </button>
                            <button onclick="shareArticle('whatsapp', '<?php echo $page_url; ?>', '<?php echo addslashes($article['title']); ?>')" 
                                    class="share-btn whatsapp" title="مشاركة على واتساب">
                                <i class="fab fa-whatsapp"></i>
                            </button>
                            <button onclick="shareArticle('telegram', '<?php echo $page_url; ?>', '<?php echo addslashes($article['title']); ?>')" 
                                    class="share-btn telegram" title="مشاركة على تيليجرام">
                                <i class="fab fa-telegram"></i>
                            </button>
                            <button onclick="copyLink('<?php echo $page_url; ?>')" 
                                    class="share-btn copy" title="نسخ الرابط">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                </header>
                
                <!-- صورة المقال -->
                <?php if ($article['image']): ?>
                <div class="article-image">
                    <img src="<?php echo SITE_URL . '/' . $article['image']; ?>" 
                         alt="<?php echo htmlspecialchars($article['title']); ?>" 
                         class="img-fluid">
                </div>
                <?php endif; ?>
                
                <!-- محتوى المقال -->
                <div class="article-content">
                    <?php echo nl2br(htmlspecialchars($article['content'])); ?>
                </div>
                
                <!-- مصدر المقال -->
                <?php if ($article['source_url']): ?>
                <div class="article-source">
                    <strong>المصدر:</strong>
                    <a href="<?php echo htmlspecialchars($article['source_url']); ?>" target="_blank" rel="noopener">
                        <?php echo htmlspecialchars($article['source_url']); ?>
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <?php endif; ?>
                
                <!-- تاريخ آخر تحديث -->
                <?php if ($article['updated_at'] !== $article['created_at']): ?>
                <div class="article-updated">
                    <small class="text-muted">
                        <i class="fas fa-edit"></i>
                        آخر تحديث: <?php echo formatDate($article['updated_at'], 'd F Y - H:i'); ?>
                    </small>
                </div>
                <?php endif; ?>
            </article>
            
            <!-- المقالات ذات الصلة -->
            <?php if (!empty($related_articles)): ?>
            <section class="related-articles mt-5">
                <h3 class="section-title">
                    <i class="fas fa-newspaper"></i>
                    مقالات ذات صلة
                </h3>
                <div class="row">
                    <?php foreach ($related_articles as $related): ?>
                    <div class="col-md-6 mb-3">
                        <?php echo renderArticleCard($related, false, true); ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>
            
            <!-- قسم التعليقات -->
            <?php if (getSetting('comments_enabled', 1)): ?>
            <section class="comments-section mt-5">
                <h3 class="section-title">
                    <i class="fas fa-comments"></i>
                    التعليقات (<?php echo $comments_count; ?>)
                </h3>
                
                <!-- نموذج إضافة تعليق -->
                <div class="comment-form-wrapper">
                    <h4>أضف تعليقك</h4>
                    <?php echo $comment_message; ?>
                    
                    <form class="comment-form" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">الاسم *</label>
                                    <input type="text" id="name" name="name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" id="email" name="email" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="content" class="form-label">التعليق *</label>
                            <textarea id="content" name="content" class="form-control" rows="4" 
                                      placeholder="اكتب تعليقك هنا..." required></textarea>
                        </div>
                        <button type="submit" name="submit_comment" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            إرسال التعليق
                        </button>
                    </form>
                </div>
                
                <!-- عرض التعليقات -->
                <?php if (!empty($comments)): ?>
                <div class="comments-list">
                    <?php foreach ($comments as $comment): ?>
                    <div class="comment-item">
                        <div class="comment-header">
                            <div class="comment-author">
                                <i class="fas fa-user-circle"></i>
                                <strong><?php echo htmlspecialchars($comment['name']); ?></strong>
                            </div>
                            <div class="comment-date">
                                <i class="fas fa-clock"></i>
                                <?php echo timeAgo($comment['created_at']); ?>
                            </div>
                        </div>
                        <div class="comment-content">
                            <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="no-comments">
                    <p>لا توجد تعليقات حتى الآن. كن أول من يعلق!</p>
                </div>
                <?php endif; ?>
            </section>
            <?php endif; ?>
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4 col-md-12">
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>
</div>

<style>
/* تنسيقات صفحة المقال */
.article-detail {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.article-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.article-category {
    margin-bottom: 15px;
}

.category-link {
    display: inline-block;
    padding: 6px 15px;
    color: var(--white-color);
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

.category-link:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.article-title {
    font-size: 32px;
    font-weight: 700;
    line-height: 1.3;
    color: var(--text-color);
    margin-bottom: 15px;
}

.article-excerpt {
    font-size: 18px;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border-right: 4px solid var(--secondary-color);
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--text-light);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.meta-item i {
    color: var(--secondary-color);
}

.article-share {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.share-label {
    font-weight: 500;
    color: var(--text-color);
}

.share-buttons {
    display: flex;
    gap: 8px;
}

.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    color: var(--white-color);
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.share-btn.facebook { background: #3b5998; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.whatsapp { background: #25d366; }
.share-btn.telegram { background: #0088cc; }
.share-btn.copy { background: var(--text-light); }

.article-image {
    margin: 30px 0;
    text-align: center;
}

.article-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.article-content {
    font-size: 16px;
    line-height: 1.8;
    color: var(--text-color);
    margin-bottom: 30px;
}

.article-content p {
    margin-bottom: 20px;
}

.article-source {
    padding: 15px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    font-size: 14px;
}

.article-source a {
    color: var(--secondary-color);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.article-source a:hover {
    text-decoration: underline;
}

.article-updated {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

/* المقالات ذات الصلة */
.related-articles {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    border: 1px solid var(--border-color);
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--secondary-color);
}

.section-title i {
    color: var(--secondary-color);
}

/* قسم التعليقات */
.comments-section {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 30px;
    border: 1px solid var(--border-color);
}

.comment-form-wrapper {
    margin-bottom: 30px;
    padding: 25px;
    background: var(--light-color);
    border-radius: var(--border-radius);
}

.comment-form-wrapper h4 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.comment-form .form-group {
    margin-bottom: 20px;
}

.comment-form .form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.comment-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 14px;
    transition: var(--transition);
}

.comment-form .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.comments-list {
    margin-top: 30px;
}

.comment-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    background: var(--white-color);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--light-color);
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.comment-author i {
    color: var(--secondary-color);
    font-size: 18px;
}

.comment-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: var(--text-light);
}

.comment-content {
    line-height: 1.6;
    color: var(--text-color);
}

.no-comments {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
    font-style: italic;
}

/* مسار التنقل */
.breadcrumb-wrapper {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 15px 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.breadcrumb {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--text-light);
}

.breadcrumb-item a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-light);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .article-detail {
        padding: 20px 15px;
    }
    
    .article-title {
        font-size: 24px;
    }
    
    .article-excerpt {
        font-size: 16px;
    }
    
    .article-meta {
        gap: 15px;
        font-size: 13px;
    }
    
    .article-share {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .share-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
    
    .article-content {
        font-size: 15px;
    }
    
    .related-articles,
    .comments-section {
        padding: 20px 15px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

@media (max-width: 576px) {
    .article-title {
        font-size: 20px;
    }
    
    .article-excerpt {
        font-size: 14px;
        padding: 12px;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .share-buttons {
        flex-wrap: wrap;
    }
    
    .breadcrumb {
        flex-wrap: wrap;
        gap: 5px;
    }
}
</style>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>

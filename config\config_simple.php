<?php
// ملف تكوين مبسط للاختبار
// انسخ هذا إلى config.php وعدّل الإعدادات

// تفعيل عرض الأخطاء للتشخيص (أزل هذا في الإنتاج)
ini_set('display_errors', 1);
error_reporting(E_ALL);

// إعدادات قاعدة البيانات - عدّل هذه القيم
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');  // غيّر هذا
define('DB_USER', 'your_username');       // غيّر هذا
define('DB_PASS', 'your_password');       // غيّر هذا

// إعدادات الموقع
define('SITE_URL', 'http://yoursite.com'); // غيّر هذا
define('SITE_NAME', 'موقع الأخبار العربي');

// إعدادات الجلسة
session_start();

// اختبار الاتصال بقاعدة البيانات
try {
    $test_connection = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $test_connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // echo "✅ الاتصال بقاعدة البيانات ناجح"; // أزل التعليق للاختبار
} catch(PDOException $e) {
    die("❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// وظائف أساسية
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

echo "<!-- ملف التكوين تم تحميله بنجاح -->";
?>

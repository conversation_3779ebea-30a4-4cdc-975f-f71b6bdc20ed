<?php
// ملف تشخيص الأخطاء - احذفه بعد حل المشكلة!

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>تشخيص النظام</h2>";

// فحص إصدار PHP
echo "<h3>1. إصدار PHP:</h3>";
echo "الإصدار الحالي: " . PHP_VERSION . "<br>";
if (version_compare(PHP_VERSION, '7.4.0') >= 0) {
    echo "✅ إصدار PHP متوافق<br>";
} else {
    echo "❌ إصدار PHP قديم - يتطلب 7.4 أو أحدث<br>";
}

// فحص الإضافات المطلوبة
echo "<h3>2. الإضافات المطلوبة:</h3>";
$required_extensions = ['mysqli', 'pdo_mysql', 'gd', 'curl', 'mbstring', 'json'];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ $ext موجود<br>";
    } else {
        echo "❌ $ext مفقود<br>";
    }
}

// فحص صلاحيات المجلدات
echo "<h3>3. صلاحيات المجلدات:</h3>";
$directories = ['uploads', 'assets', 'assets/images', 'config'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "✅ $dir قابل للكتابة<br>";
        } else {
            echo "❌ $dir غير قابل للكتابة<br>";
        }
    } else {
        echo "❌ $dir غير موجود<br>";
    }
}

// فحص ملف التكوين
echo "<h3>4. ملف التكوين:</h3>";
if (file_exists('config/config.php')) {
    echo "✅ ملف config.php موجود<br>";
    
    // محاولة تضمين الملف
    try {
        require_once 'config/config.php';
        echo "✅ تم تحميل ملف التكوين بنجاح<br>";
        
        // فحص الثوابت المطلوبة
        $required_constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS', 'SITE_URL'];
        foreach ($required_constants as $const) {
            if (defined($const)) {
                echo "✅ $const محدد<br>";
            } else {
                echo "❌ $const غير محدد<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في ملف التكوين: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف config.php غير موجود<br>";
}

// فحص الاتصال بقاعدة البيانات
echo "<h3>5. اتصال قاعدة البيانات:</h3>";
if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
    } catch (PDOException $e) {
        echo "❌ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ إعدادات قاعدة البيانات غير مكتملة<br>";
}

// فحص ملف .htaccess
echo "<h3>6. ملف .htaccess:</h3>";
if (file_exists('.htaccess')) {
    echo "✅ ملف .htaccess موجود<br>";
    
    // فحص mod_rewrite
    if (function_exists('apache_get_modules')) {
        $modules = apache_get_modules();
        if (in_array('mod_rewrite', $modules)) {
            echo "✅ mod_rewrite مفعل<br>";
        } else {
            echo "❌ mod_rewrite غير مفعل<br>";
        }
    } else {
        echo "⚠️ لا يمكن فحص mod_rewrite<br>";
    }
} else {
    echo "❌ ملف .htaccess غير موجود<br>";
}

// معلومات الخادم
echo "<h3>7. معلومات الخادم:</h3>";
echo "نوع الخادم: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "مجلد الجذر: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "المجلد الحالي: " . __DIR__ . "<br>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> احذف هذا الملف بعد حل المشكلة لأسباب أمنية!</p>";
?>

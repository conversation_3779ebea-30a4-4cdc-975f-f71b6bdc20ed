<?php
// الحصول على البيانات للشريط الجانبي
$popular_articles = getArticles(5, 0, null, 'published');
$recent_articles = getArticles(5, 0, null, 'published');
$sidebar_categories = getCategories();
$stats = getStats();

// ترتيب المقالات حسب المشاهدات
$db->query("SELECT a.*, c.name as category_name, c.slug as category_slug, c.color as category_color
            FROM articles a 
            LEFT JOIN categories c ON a.category_id = c.id 
            WHERE a.status = 'published' 
            ORDER BY a.views DESC 
            LIMIT 5");
$popular_articles = $db->resultset();
?>

<aside class="sidebar">
    <!-- البحث السريع -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-search"></i>
            البحث السريع
        </div>
        <div class="widget-content">
            <form class="sidebar-search-form" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                <div class="search-group">
                    <input type="text" name="q" class="form-control" placeholder="ابحث في الأخبار..." 
                           value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                    <button type="submit" class="search-submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- الأخبار الأكثر قراءة -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-fire"></i>
            الأكثر قراءة
        </div>
        <div class="widget-content">
            <?php if (!empty($popular_articles)): ?>
                <div class="popular-articles">
                    <?php foreach ($popular_articles as $index => $article): ?>
                    <div class="popular-article-item">
                        <div class="popular-number"><?php echo $index + 1; ?></div>
                        <div class="popular-content">
                            <h4 class="popular-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                    <?php echo htmlspecialchars(truncateText($article['title'], 80)); ?>
                                </a>
                            </h4>
                            <div class="popular-meta">
                                <?php if ($article['category_name']): ?>
                                    <span class="popular-category" style="color: <?php echo $article['category_color']; ?>">
                                        <?php echo htmlspecialchars($article['category_name']); ?>
                                    </span>
                                <?php endif; ?>
                                <span class="popular-views">
                                    <i class="fas fa-eye"></i>
                                    <?php echo number_format($article['views']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="no-content">لا توجد مقالات متاحة حالياً</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- أحدث المقالات -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-clock"></i>
            أحدث المقالات
        </div>
        <div class="widget-content">
            <?php if (!empty($recent_articles)): ?>
                <div class="recent-articles">
                    <?php foreach ($recent_articles as $article): ?>
                    <div class="recent-article-item">
                        <div class="recent-image">
                            <?php 
                            $image_url = $article['image'] ? SITE_URL . '/' . $article['image'] : SITE_URL . '/assets/images/default-article.jpg';
                            ?>
                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                <img src="<?php echo $image_url; ?>" alt="<?php echo htmlspecialchars($article['title']); ?>" loading="lazy">
                            </a>
                        </div>
                        <div class="recent-content">
                            <h4 class="recent-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                    <?php echo htmlspecialchars(truncateText($article['title'], 70)); ?>
                                </a>
                            </h4>
                            <div class="recent-meta">
                                <span class="recent-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="no-content">لا توجد مقالات متاحة حالياً</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- الفئات -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-tags"></i>
            الفئات
        </div>
        <div class="widget-content">
            <?php if (!empty($sidebar_categories)): ?>
                <div class="categories-list">
                    <?php foreach ($sidebar_categories as $category): ?>
                    <?php
                    // حساب عدد المقالات في كل فئة
                    $article_count = countArticles($category['id'], 'published');
                    ?>
                    <div class="category-item">
                        <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" 
                           class="category-link">
                            <span class="category-color" style="background-color: <?php echo $category['color']; ?>"></span>
                            <span class="category-name"><?php echo htmlspecialchars($category['name']); ?></span>
                            <span class="category-count"><?php echo $article_count; ?></span>
                        </a>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <p class="no-content">لا توجد فئات متاحة حالياً</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- إحصائيات الموقع -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-chart-bar"></i>
            إحصائيات الموقع
        </div>
        <div class="widget-content">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo number_format($stats['articles']); ?></div>
                        <div class="stat-label">مقال</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo number_format($stats['categories']); ?></div>
                        <div class="stat-label">فئة</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo number_format($stats['comments']); ?></div>
                        <div class="stat-label">تعليق</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number"><?php echo number_format($stats['views']); ?></div>
                        <div class="stat-label">مشاهدة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إعلان (اختياري) -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-bullhorn"></i>
            إعلان
        </div>
        <div class="widget-content">
            <div class="advertisement">
                <div class="ad-placeholder">
                    <i class="fas fa-ad"></i>
                    <p>مساحة إعلانية</p>
                    <small>300 × 250</small>
                </div>
            </div>
        </div>
    </div>

    <!-- تابعنا -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-share-alt"></i>
            تابعنا
        </div>
        <div class="widget-content">
            <div class="social-follow">
                <a href="#" class="social-follow-item facebook">
                    <i class="fab fa-facebook-f"></i>
                    <span>فيسبوك</span>
                    <div class="follow-count">12.5K</div>
                </a>
                <a href="#" class="social-follow-item twitter">
                    <i class="fab fa-twitter"></i>
                    <span>تويتر</span>
                    <div class="follow-count">8.2K</div>
                </a>
                <a href="#" class="social-follow-item instagram">
                    <i class="fab fa-instagram"></i>
                    <span>إنستغرام</span>
                    <div class="follow-count">15.7K</div>
                </a>
                <a href="#" class="social-follow-item youtube">
                    <i class="fab fa-youtube"></i>
                    <span>يوتيوب</span>
                    <div class="follow-count">5.3K</div>
                </a>
                <a href="#" class="social-follow-item telegram">
                    <i class="fab fa-telegram"></i>
                    <span>تيليجرام</span>
                    <div class="follow-count">3.1K</div>
                </a>
            </div>
        </div>
    </div>

    <!-- النشرة الإخبارية -->
    <div class="sidebar-widget">
        <div class="widget-header">
            <i class="fas fa-envelope"></i>
            النشرة الإخبارية
        </div>
        <div class="widget-content">
            <div class="newsletter-signup">
                <p>احصل على آخر الأخبار مباشرة في بريدك الإلكتروني</p>
                <form class="newsletter-form" action="<?php echo SITE_URL; ?>/subscribe.php" method="POST">
                    <div class="form-group">
                        <input type="email" name="email" class="form-control" placeholder="بريدك الإلكتروني" required>
                    </div>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-paper-plane"></i>
                        اشترك الآن
                    </button>
                </form>
            </div>
        </div>
    </div>
</aside>

<style>
/* تنسيقات الشريط الجانبي */
.sidebar {
    padding-right: 30px;
}

.sidebar-widget {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.widget-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    color: var(--white-color);
    padding: 15px 20px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-content {
    padding: 20px;
}

/* البحث السريع */
.search-group {
    display: flex;
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 2px solid var(--border-color);
}

.search-group input {
    flex: 1;
    border: none;
    padding: 12px 15px;
    font-size: 14px;
}

.search-group input:focus {
    outline: none;
}

.search-submit {
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    padding: 12px 15px;
    cursor: pointer;
    transition: var(--transition);
}

.search-submit:hover {
    background: #2980b9;
}

/* المقالات الشائعة */
.popular-articles {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.popular-article-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--light-color);
}

.popular-article-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.popular-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
}

.popular-content {
    flex: 1;
}

.popular-title {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.popular-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.popular-title a:hover {
    color: var(--secondary-color);
}

.popular-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
}

.popular-category {
    font-weight: 500;
}

.popular-views {
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 4px;
}

/* أحدث المقالات */
.recent-articles {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recent-article-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--light-color);
}

.recent-article-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.recent-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
}

.recent-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.recent-image:hover img {
    transform: scale(1.1);
}

.recent-content {
    flex: 1;
}

.recent-title {
    margin: 0 0 8px 0;
    font-size: 13px;
    line-height: 1.4;
}

.recent-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.recent-title a:hover {
    color: var(--secondary-color);
}

.recent-meta {
    font-size: 11px;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 4px;
}

/* الفئات */
.categories-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.category-item {
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--light-color);
    transition: var(--transition);
}

.category-item:hover {
    border-color: var(--secondary-color);
    transform: translateX(-3px);
}

.category-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.category-name {
    flex: 1;
    font-weight: 500;
}

.category-count {
    background: var(--light-color);
    color: var(--text-light);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.stat-item:hover {
    background: var(--secondary-color);
    color: var(--white-color);
    transform: translateY(-2px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--secondary-color);
    color: var(--white-color);
    border-radius: 50%;
    font-size: 16px;
}

.stat-item:hover .stat-icon {
    background: var(--white-color);
    color: var(--secondary-color);
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: var(--text-light);
}

.stat-item:hover .stat-label {
    color: rgba(255,255,255,0.8);
}

/* الإعلان */
.advertisement {
    text-align: center;
}

.ad-placeholder {
    background: var(--light-color);
    padding: 40px 20px;
    border-radius: var(--border-radius);
    color: var(--text-light);
    border: 2px dashed var(--border-color);
}

.ad-placeholder i {
    font-size: 40px;
    margin-bottom: 10px;
    display: block;
}

/* وسائل التواصل */
.social-follow {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.social-follow-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
    color: var(--white-color);
}

.social-follow-item.facebook { background: #3b5998; }
.social-follow-item.twitter { background: #1da1f2; }
.social-follow-item.instagram { background: #e4405f; }
.social-follow-item.youtube { background: #ff0000; }
.social-follow-item.telegram { background: #0088cc; }

.social-follow-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow);
}

.social-follow-item i {
    font-size: 18px;
    width: 20px;
}

.social-follow-item span {
    flex: 1;
    font-weight: 500;
}

.follow-count {
    font-size: 12px;
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 12px;
}

/* النشرة الإخبارية */
.newsletter-signup p {
    margin-bottom: 15px;
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
}

.newsletter-form .form-group {
    margin-bottom: 15px;
}

.no-content {
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    margin: 0;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        padding-right: 0;
        margin-top: 30px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .popular-article-item,
    .recent-article-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .popular-number {
        align-self: flex-start;
    }
    
    .recent-image {
        width: 100%;
        height: 150px;
    }
}
</style>

// وظائف JavaScript للوحة التحكم الإدارية

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الوظائف
    initAdminDashboard();
    initDataTables();
    initFormValidation();
    initFileUploads();
    initRichTextEditors();
    initDatePickers();
    initTooltips();
});

// تهيئة لوحة التحكم
function initAdminDashboard() {
    // تحديث الإحصائيات كل 5 دقائق
    setInterval(updateDashboardStats, 300000);
    
    // تهيئة الرسوم البيانية
    initCharts();
    
    // تهيئة الإشعارات
    checkNotifications();
    setInterval(checkNotifications, 60000); // كل دقيقة
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    fetch('ajax/get_stats.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatCard('articles', data.stats.articles);
                updateStatCard('categories', data.stats.categories);
                updateStatCard('comments', data.stats.comments);
                updateStatCard('views', data.stats.views);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

// تحديث بطاقة إحصائية
function updateStatCard(type, value) {
    const card = document.querySelector(`.stat-card.${type} .stat-number`);
    if (card) {
        // تأثير العد التصاعدي
        animateNumber(card, parseInt(card.textContent.replace(/,/g, '')), value);
    }
}

// تأثير العد التصاعدي للأرقام
function animateNumber(element, start, end, duration = 1000) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString('ar-SA');
    }, 16);
}

// تهيئة الجداول
function initDataTables() {
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        // إضافة وظائف الفرز والبحث
        addTableSorting(table);
        addTableSearch(table);
        addTablePagination(table);
    });
}

// إضافة فرز الجدول
function addTableSorting(table) {
    const headers = table.querySelectorAll('th[data-sortable]');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(table, this);
        });
    });
}

// فرز الجدول
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // إزالة فئات الفرز من جميع الرؤوس
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // إضافة فئة الفرز للرأس الحالي
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // فرز الصفوف
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // التحقق من الأرقام
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // فرز النصوص
        return isAscending ? 
            aValue.localeCompare(bValue, 'ar') : 
            bValue.localeCompare(aValue, 'ar');
    });
    
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
}

// إضافة بحث الجدول
function addTableSearch(table) {
    const searchInput = table.parentNode.querySelector('.table-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterTable(table, this.value);
        });
    }
}

// فلترة الجدول
function filterTable(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(term) ? '' : 'none';
    });
}

// تهيئة التحقق من النماذج
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // التحقق الفوري من الحقول
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
    });
}

// التحقق من حقل واحد
function validateField(field) {
    const isValid = field.checkValidity();
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    
    field.classList.toggle('is-valid', isValid);
    field.classList.toggle('is-invalid', !isValid);
    
    if (feedback) {
        feedback.style.display = isValid ? 'none' : 'block';
    }
}

// تهيئة رفع الملفات
function initFileUploads() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFileUpload(this);
        });
    });
}

// معالجة رفع الملفات
function handleFileUpload(input) {
    const files = input.files;
    const preview = input.parentNode.querySelector('.file-preview');
    
    if (files.length > 0 && preview) {
        const file = files[0];
        
        // التحقق من نوع الملف
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="معاينة" style="max-width: 200px; max-height: 200px;">`;
            };
            reader.readAsDataURL(file);
        } else {
            preview.innerHTML = `<p>تم اختيار الملف: ${file.name}</p>`;
        }
    }
}

// تهيئة محررات النصوص الغنية
function initRichTextEditors() {
    const textareas = document.querySelectorAll('.rich-editor');
    textareas.forEach(textarea => {
        // يمكن إضافة مكتبة محرر نصوص مثل TinyMCE أو CKEditor هنا
        addBasicFormatting(textarea);
    });
}

// إضافة تنسيق أساسي للنصوص
function addBasicFormatting(textarea) {
    const toolbar = document.createElement('div');
    toolbar.className = 'editor-toolbar';
    toolbar.innerHTML = `
        <button type="button" onclick="formatText('bold')" title="عريض"><i class="fas fa-bold"></i></button>
        <button type="button" onclick="formatText('italic')" title="مائل"><i class="fas fa-italic"></i></button>
        <button type="button" onclick="formatText('underline')" title="تحته خط"><i class="fas fa-underline"></i></button>
        <button type="button" onclick="insertLink()" title="رابط"><i class="fas fa-link"></i></button>
    `;
    
    textarea.parentNode.insertBefore(toolbar, textarea);
}

// تنسيق النص
function formatText(command) {
    document.execCommand(command, false, null);
}

// إدراج رابط
function insertLink() {
    const url = prompt('أدخل الرابط:');
    if (url) {
        document.execCommand('createLink', false, url);
    }
}

// تهيئة منتقي التواريخ
function initDatePickers() {
    const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
    dateInputs.forEach(input => {
        // إضافة تنسيق عربي للتواريخ
        input.addEventListener('change', function() {
            formatArabicDate(this);
        });
    });
}

// تنسيق التاريخ بالعربية
function formatArabicDate(input) {
    if (input.value) {
        const date = new Date(input.value);
        const options = { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
        };
        const arabicDate = date.toLocaleDateString('ar-SA', options);
        
        // عرض التاريخ المنسق
        let display = input.parentNode.querySelector('.date-display');
        if (!display) {
            display = document.createElement('small');
            display.className = 'date-display text-muted';
            input.parentNode.appendChild(display);
        }
        display.textContent = arabicDate;
    }
}

// تهيئة التلميحات
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

// عرض التلميح
function showTooltip(e) {
    const text = e.target.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = text;
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    
    setTimeout(() => tooltip.classList.add('show'), 10);
}

// إخفاء التلميح
function hideTooltip() {
    const tooltip = document.querySelector('.custom-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// تهيئة الرسوم البيانية
function initCharts() {
    // رسم بياني للمشاهدات
    const viewsChart = document.getElementById('viewsChart');
    if (viewsChart) {
        createViewsChart(viewsChart);
    }
    
    // رسم بياني للمقالات
    const articlesChart = document.getElementById('articlesChart');
    if (articlesChart) {
        createArticlesChart(articlesChart);
    }
}

// إنشاء رسم بياني للمشاهدات
function createViewsChart(canvas) {
    // يمكن استخدام مكتبة Chart.js هنا
    const ctx = canvas.getContext('2d');
    
    // رسم بياني بسيط
    ctx.fillStyle = '#3498db';
    ctx.fillRect(10, 10, 50, 100);
    ctx.fillStyle = '#27ae60';
    ctx.fillRect(70, 30, 50, 80);
    ctx.fillStyle = '#f39c12';
    ctx.fillRect(130, 50, 50, 60);
}

// فحص الإشعارات
function checkNotifications() {
    fetch('ajax/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationBadge(data.count);
                updateNotificationList(data.notifications);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب الإشعارات:', error);
        });
}

// تحديث شارة الإشعارات
function updateNotificationBadge(count) {
    const badge = document.querySelector('.notification-badge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }
}

// تحديث قائمة الإشعارات
function updateNotificationList(notifications) {
    const list = document.querySelector('.notification-list');
    if (list && notifications.length > 0) {
        list.innerHTML = notifications.map(notification => `
            <a href="${notification.url}" class="notification-item">
                <div class="notification-icon">
                    <i class="${notification.icon}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-text">${notification.text}</div>
                </div>
            </a>
        `).join('');
    }
}

// وظائف AJAX للتعليقات
function approveComment(commentId) {
    updateCommentStatus(commentId, 'approved');
}

function rejectComment(commentId) {
    updateCommentStatus(commentId, 'rejected');
}

function updateCommentStatus(commentId, status) {
    showLoadingModal('جاري تحديث التعليق...');
    
    fetch('ajax/update_comment_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            comment_id: commentId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showAdminMessage('تم تحديث حالة التعليق بنجاح', 'success');
            // تحديث واجهة المستخدم
            location.reload();
        } else {
            showAdminMessage(data.message || 'حدث خطأ أثناء تحديث التعليق', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        showAdminMessage('حدث خطأ في الاتصال', 'error');
        console.error('Error:', error);
    });
}

// حذف عنصر
function deleteItem(type, id, name = '') {
    const message = name ? 
        `هل أنت متأكد من حذف "${name}"؟` : 
        'هل أنت متأكد من حذف هذا العنصر؟';
    
    showDeleteModal(message, function() {
        performDelete(type, id);
    });
}

// تنفيذ الحذف
function performDelete(type, id) {
    showLoadingModal('جاري الحذف...');
    
    fetch(`ajax/delete_${type}.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: id })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showAdminMessage('تم الحذف بنجاح', 'success');
            // إزالة العنصر من الواجهة
            const element = document.querySelector(`[data-id="${id}"]`);
            if (element) {
                element.remove();
            }
        } else {
            showAdminMessage(data.message || 'حدث خطأ أثناء الحذف', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        showAdminMessage('حدث خطأ في الاتصال', 'error');
        console.error('Error:', error);
    });
}

// تبديل حالة العنصر
function toggleStatus(type, id, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    fetch(`ajax/toggle_${type}_status.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            id: id, 
            status: newStatus 
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAdminMessage('تم تحديث الحالة بنجاح', 'success');
            // تحديث واجهة المستخدم
            const statusElement = document.querySelector(`[data-status-id="${id}"]`);
            if (statusElement) {
                statusElement.textContent = newStatus === 'active' ? 'نشط' : 'غير نشط';
                statusElement.className = `status ${newStatus}`;
            }
        } else {
            showAdminMessage(data.message || 'حدث خطأ أثناء التحديث', 'error');
        }
    })
    .catch(error => {
        showAdminMessage('حدث خطأ في الاتصال', 'error');
        console.error('Error:', error);
    });
}

// تصدير البيانات
function exportData(type, format = 'csv') {
    showLoadingModal('جاري تصدير البيانات...');
    
    const link = document.createElement('a');
    link.href = `ajax/export_${type}.php?format=${format}`;
    link.download = `${type}_export_${new Date().toISOString().split('T')[0]}.${format}`;
    link.click();
    
    setTimeout(() => {
        hideLoadingModal();
        showAdminMessage('تم تصدير البيانات بنجاح', 'success');
    }, 2000);
}

// نسخ احتياطية
function createBackup() {
    showLoadingModal('جاري إنشاء النسخة الاحتياطية...');
    
    fetch('ajax/create_backup.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showAdminMessage('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            // تحميل النسخة الاحتياطية
            const link = document.createElement('a');
            link.href = data.backup_url;
            link.download = data.backup_filename;
            link.click();
        } else {
            showAdminMessage(data.message || 'حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        showAdminMessage('حدث خطأ في الاتصال', 'error');
        console.error('Error:', error);
    });
}

// تحديث إعدادات الموقع
function updateSiteSettings(formData) {
    showLoadingModal('جاري حفظ الإعدادات...');
    
    fetch('ajax/update_settings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showAdminMessage('تم حفظ الإعدادات بنجاح', 'success');
            setUnsavedChanges(false);
        } else {
            showAdminMessage(data.message || 'حدث خطأ أثناء حفظ الإعدادات', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        showAdminMessage('حدث خطأ في الاتصال', 'error');
        console.error('Error:', error);
    });
}

// تهيئة اختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Ctrl+S للحفظ
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        const saveBtn = document.querySelector('.btn-save, button[type="submit"]');
        if (saveBtn) {
            saveBtn.click();
        }
    }
    
    // Ctrl+N لإضافة جديد
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        const addBtn = document.querySelector('.btn-add-new');
        if (addBtn) {
            addBtn.click();
        }
    }
});

console.log('تم تحميل JavaScript للوحة التحكم بنجاح');

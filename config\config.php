<?php
// إعدادات عامة للموقع
session_start();

// إعدادات الموقع
define('SITE_URL', 'http://localhost/amr');
define('SITE_PATH', dirname(dirname(__FILE__)));
define('ADMIN_PATH', SITE_PATH . '/admin');
define('UPLOADS_PATH', SITE_PATH . '/uploads');
define('UPLOADS_URL', SITE_URL . '/uploads');

// إعدادات الأمان
define('HASH_ALGORITHM', 'sha256');
define('ENCRYPTION_KEY', 'your-secret-key-here');

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS

// إعدادات الوقت
date_default_timezone_set('Asia/Riyadh');

// إعدادات الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// تضمين ملفات الإعدادات
require_once SITE_PATH . '/config/database.php';
require_once SITE_PATH . '/includes/functions.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();

// إنشاء قاعدة البيانات والجداول إذا لم تكن موجودة
if (!$db->createDatabase()) {
    die('خطأ في إنشاء قاعدة البيانات');
}

// وظائف مساعدة عامة
function getSetting($key, $default = '') {
    global $db;
    $db->query("SELECT setting_value FROM settings WHERE setting_key = ?");
    $db->bind(1, $key);
    $result = $db->single();
    return $result ? $result['setting_value'] : $default;
}

function setSetting($key, $value) {
    global $db;
    $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    $db->bind(1, $key);
    $db->bind(2, $value);
    $db->bind(3, $value);
    return $db->execute();
}

function redirect($url) {
    header("Location: $url");
    exit();
}

function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function generateSlug($text) {
    // تحويل النص العربي إلى slug
    $text = trim($text);
    $text = preg_replace('/\s+/', '-', $text);
    $text = preg_replace('/[^\p{Arabic}\p{L}\p{N}\-_]/u', '', $text);
    return strtolower($text);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'منذ لحظات';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
    return 'منذ ' . floor($time/31536000) . ' سنة';
}

function formatDate($date, $format = 'Y-m-d H:i') {
    return date($format, strtotime($date));
}

function truncateText($text, $length = 150) {
    if (mb_strlen($text, 'UTF-8') <= $length) {
        return $text;
    }
    return mb_substr($text, 0, $length, 'UTF-8') . '...';
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect(SITE_URL . '/admin/login.php');
    }
}

function hasPermission($required_role) {
    if (!isLoggedIn()) return false;
    
    $roles = ['author' => 1, 'editor' => 2, 'admin' => 3];
    $user_role = $_SESSION['user_role'] ?? 'author';
    
    return $roles[$user_role] >= $roles[$required_role];
}

function uploadFile($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif'], $max_size = 5242880) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $upload_dir = UPLOADS_PATH . '/' . date('Y/m');
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $filepath = $upload_dir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        $relative_path = 'uploads/' . date('Y/m') . '/' . $filename;
        return ['success' => true, 'path' => $relative_path, 'url' => SITE_URL . '/' . $relative_path];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

function deleteFile($path) {
    $full_path = SITE_PATH . '/' . $path;
    if (file_exists($full_path)) {
        return unlink($full_path);
    }
    return false;
}

// إعدادات الترقيم
function getPagination($current_page, $total_pages, $base_url) {
    $pagination = '';
    
    if ($total_pages <= 1) return $pagination;
    
    $pagination .= '<nav aria-label="ترقيم الصفحات">';
    $pagination .= '<ul class="pagination justify-content-center">';
    
    // الصفحة السابقة
    if ($current_page > 1) {
        $prev_page = $current_page - 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $prev_page . '">السابق</a>';
        $pagination .= '</li>';
    }
    
    // أرقام الصفحات
    $start = max(1, $current_page - 2);
    $end = min($total_pages, $current_page + 2);
    
    if ($start > 1) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '?page=1">1</a></li>';
        if ($start > 2) {
            $pagination .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        $active = ($i == $current_page) ? 'active' : '';
        $pagination .= '<li class="page-item ' . $active . '">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $i . '">' . $i . '</a>';
        $pagination .= '</li>';
    }
    
    if ($end < $total_pages) {
        if ($end < $total_pages - 1) {
            $pagination .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $base_url . '?page=' . $total_pages . '">' . $total_pages . '</a></li>';
    }
    
    // الصفحة التالية
    if ($current_page < $total_pages) {
        $next_page = $current_page + 1;
        $pagination .= '<li class="page-item">';
        $pagination .= '<a class="page-link" href="' . $base_url . '?page=' . $next_page . '">التالي</a>';
        $pagination .= '</li>';
    }
    
    $pagination .= '</ul>';
    $pagination .= '</nav>';
    
    return $pagination;
}

// تنظيف المخرجات
function cleanOutput($content) {
    return trim(preg_replace('/\s+/', ' ', $content));
}

// إعدادات SEO
function generateMetaTags($title = '', $description = '', $image = '', $url = '') {
    $site_name = getSetting('site_name', 'موقع الأخبار العربي');
    $site_description = getSetting('site_description', 'موقع إخباري عربي شامل');
    
    $title = $title ? $title . ' - ' . $site_name : $site_name;
    $description = $description ?: $site_description;
    $url = $url ?: SITE_URL . $_SERVER['REQUEST_URI'];
    $image = $image ? SITE_URL . '/' . $image : SITE_URL . '/assets/images/logo.png';
    
    $meta = '';
    $meta .= '<title>' . htmlspecialchars($title) . '</title>' . "\n";
    $meta .= '<meta name="description" content="' . htmlspecialchars($description) . '">' . "\n";
    $meta .= '<meta property="og:title" content="' . htmlspecialchars($title) . '">' . "\n";
    $meta .= '<meta property="og:description" content="' . htmlspecialchars($description) . '">' . "\n";
    $meta .= '<meta property="og:image" content="' . htmlspecialchars($image) . '">' . "\n";
    $meta .= '<meta property="og:url" content="' . htmlspecialchars($url) . '">' . "\n";
    $meta .= '<meta property="og:type" content="article">' . "\n";
    $meta .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
    $meta .= '<meta name="twitter:title" content="' . htmlspecialchars($title) . '">' . "\n";
    $meta .= '<meta name="twitter:description" content="' . htmlspecialchars($description) . '">' . "\n";
    $meta .= '<meta name="twitter:image" content="' . htmlspecialchars($image) . '">' . "\n";
    
    return $meta;
}
?>

// الوظائف الرئيسية للموقع الإخباري العربي

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الوظائف
    initMobileMenu();
    initSearch();
    initBreakingNews();
    initScrollToTop();
    initLazyLoading();
    initComments();
    initSmoothScroll();
    initProgressBar();
});

// قائمة الهاتف المحمول
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navbar = document.querySelector('.navbar-nav');
    
    if (mobileToggle && navbar) {
        mobileToggle.addEventListener('click', function() {
            navbar.classList.toggle('active');
            this.classList.toggle('active');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !navbar.contains(e.target)) {
                navbar.classList.remove('active');
                mobileToggle.classList.remove('active');
            }
        });
    }
}

// البحث المباشر
function initSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('.search-results');
    let searchTimeout;
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performLiveSearch(query);
                }, 300);
            } else {
                hideSearchResults();
            }
        });
        
        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults?.contains(e.target)) {
                hideSearchResults();
            }
        });
    }
}

// البحث المباشر
function performLiveSearch(query) {
    fetch(`search_ajax.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function displaySearchResults(results) {
    const searchResults = document.querySelector('.search-results');
    
    if (!searchResults) {
        createSearchResultsContainer();
    }
    
    const container = document.querySelector('.search-results');
    
    if (results.length === 0) {
        container.innerHTML = '<div class="no-results">لا توجد نتائج</div>';
    } else {
        let html = '';
        results.forEach(article => {
            html += `
                <div class="search-result-item">
                    <a href="article.php?slug=${article.slug}">
                        <div class="result-title">${article.title}</div>
                        <div class="result-excerpt">${article.excerpt}</div>
                        <div class="result-meta">${article.category_name} • ${article.date}</div>
                    </a>
                </div>
            `;
        });
        container.innerHTML = html;
    }
    
    container.style.display = 'block';
}

// إنشاء حاوي نتائج البحث
function createSearchResultsContainer() {
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'search-results';
        searchForm.appendChild(resultsDiv);
    }
}

// إخفاء نتائج البحث
function hideSearchResults() {
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// شريط الأخبار العاجلة
function initBreakingNews() {
    const breakingNews = document.querySelector('.breaking-news');
    
    if (breakingNews) {
        // إيقاف التمرير عند التمرير فوق الشريط
        breakingNews.addEventListener('mouseenter', function() {
            const scrollText = this.querySelector('.breaking-text');
            if (scrollText) {
                scrollText.style.animationPlayState = 'paused';
            }
        });
        
        // استئناف التمرير عند مغادرة الشريط
        breakingNews.addEventListener('mouseleave', function() {
            const scrollText = this.querySelector('.breaking-text');
            if (scrollText) {
                scrollText.style.animationPlayState = 'running';
            }
        });
    }
}

// زر العودة للأعلى
function initScrollToTop() {
    const scrollBtn = document.createElement('button');
    scrollBtn.className = 'scroll-to-top';
    scrollBtn.innerHTML = '↑';
    scrollBtn.title = 'العودة للأعلى';
    document.body.appendChild(scrollBtn);
    
    // إظهار/إخفاء الزر حسب موضع التمرير
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollBtn.classList.add('visible');
        } else {
            scrollBtn.classList.remove('visible');
        }
    });
    
    // العودة للأعلى عند النقر
    scrollBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// التحميل التدريجي للصور
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // بديل للمتصفحات القديمة
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
}

// نظام التعليقات
function initComments() {
    const commentForm = document.querySelector('.comment-form');
    
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitComment(this);
        });
    }
    
    // أزرار الرد على التعليقات
    const replyButtons = document.querySelectorAll('.reply-btn');
    replyButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            toggleReplyForm(this);
        });
    });
}

// إرسال تعليق
function submitComment(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // تعطيل الزر أثناء الإرسال
    submitBtn.disabled = true;
    submitBtn.textContent = 'جاري الإرسال...';
    
    fetch('submit_comment.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('تم إرسال التعليق بنجاح وسيتم مراجعته قريباً', 'success');
            form.reset();
        } else {
            showMessage(data.message || 'حدث خطأ أثناء إرسال التعليق', 'error');
        }
    })
    .catch(error => {
        showMessage('حدث خطأ في الاتصال', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = 'إرسال التعليق';
    });
}

// إظهار/إخفاء نموذج الرد
function toggleReplyForm(button) {
    const commentId = button.dataset.commentId;
    const existingForm = document.querySelector(`.reply-form[data-comment-id="${commentId}"]`);
    
    if (existingForm) {
        existingForm.remove();
    } else {
        const replyForm = createReplyForm(commentId);
        button.parentNode.appendChild(replyForm);
    }
}

// إنشاء نموذج الرد
function createReplyForm(commentId) {
    const form = document.createElement('div');
    form.className = 'reply-form';
    form.dataset.commentId = commentId;
    form.innerHTML = `
        <form class="comment-form">
            <input type="hidden" name="parent_id" value="${commentId}">
            <div class="form-group">
                <input type="text" name="name" placeholder="الاسم" required>
            </div>
            <div class="form-group">
                <input type="email" name="email" placeholder="البريد الإلكتروني" required>
            </div>
            <div class="form-group">
                <textarea name="content" placeholder="اكتب ردك هنا..." required></textarea>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">إرسال الرد</button>
                <button type="button" class="btn btn-secondary cancel-reply">إلغاء</button>
            </div>
        </form>
    `;
    
    // إضافة مستمع للإلغاء
    form.querySelector('.cancel-reply').addEventListener('click', function() {
        form.remove();
    });
    
    // إضافة مستمع للإرسال
    form.querySelector('.comment-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitComment(this);
    });
    
    return form;
}

// التمرير السلس
function initSmoothScroll() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// شريط التقدم
function initProgressBar() {
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', function() {
        const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
        const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = (winScroll / height) * 100;
        progressBar.style.width = scrolled + '%';
    });
}

// عرض الرسائل
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type} message-popup`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // إظهار الرسالة
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            messageDiv.remove();
        }, 300);
    }, 5000);
}

// تحميل المزيد من المقالات
function loadMoreArticles(page, category = '') {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    const articlesContainer = document.querySelector('.articles-container');
    
    if (loadMoreBtn) {
        loadMoreBtn.textContent = 'جاري التحميل...';
        loadMoreBtn.disabled = true;
    }
    
    const url = `load_more.php?page=${page}&category=${category}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.articles) {
                articlesContainer.insertAdjacentHTML('beforeend', data.articles);
                
                if (data.hasMore) {
                    loadMoreBtn.textContent = 'تحميل المزيد';
                    loadMoreBtn.disabled = false;
                    loadMoreBtn.dataset.page = parseInt(page) + 1;
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المقالات:', error);
            if (loadMoreBtn) {
                loadMoreBtn.textContent = 'تحميل المزيد';
                loadMoreBtn.disabled = false;
            }
        });
}

// مشاركة المقال
function shareArticle(platform, url, title) {
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
        whatsapp: `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`,
        telegram: `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`
    };
    
    if (shareUrls[platform]) {
        window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
}

// نسخ الرابط
function copyLink(url) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showMessage('تم نسخ الرابط بنجاح', 'success');
        });
    } else {
        // بديل للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showMessage('تم نسخ الرابط بنجاح', 'success');
    }
}

// تبديل الوضع الليلي
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    const isDark = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDark);
}

// تحميل إعدادات الوضع الليلي
function loadDarkMode() {
    const isDark = localStorage.getItem('darkMode') === 'true';
    if (isDark) {
        document.body.classList.add('dark-mode');
    }
}

// تهيئة الوضع الليلي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', loadDarkMode);

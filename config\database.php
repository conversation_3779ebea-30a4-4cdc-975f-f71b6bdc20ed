<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'arabic_news');

class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;
    private $dbh;
    private $error;

    public function __construct() {
        // إعداد DSN
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname . ';charset=utf8mb4';
        
        // إعداد الخيارات
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        );

        // إنشاء اتصال PDO
        try {
            $this->dbh = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
        }
    }

    // تحضير الاستعلام
    public function query($query) {
        $this->stmt = $this->dbh->prepare($query);
    }

    // ربط القيم
    public function bind($param, $value, $type = null) {
        if (is_null($type)) {
            switch (true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }
        $this->stmt->bindValue($param, $value, $type);
    }

    // تنفيذ الاستعلام
    public function execute() {
        return $this->stmt->execute();
    }

    // الحصول على النتائج كمصفوفة
    public function resultset() {
        $this->execute();
        return $this->stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // الحصول على نتيجة واحدة
    public function single() {
        $this->execute();
        return $this->stmt->fetch(PDO::FETCH_ASSOC);
    }

    // عدد الصفوف
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // آخر معرف مدرج
    public function lastInsertId() {
        return $this->dbh->lastInsertId();
    }

    // بدء المعاملة
    public function beginTransaction() {
        return $this->dbh->beginTransaction();
    }

    // تأكيد المعاملة
    public function commit() {
        return $this->dbh->commit();
    }

    // إلغاء المعاملة
    public function rollBack() {
        return $this->dbh->rollBack();
    }

    // إنشاء قاعدة البيانات والجداول
    public function createDatabase() {
        try {
            // إنشاء قاعدة البيانات
            $pdo = new PDO('mysql:host=' . $this->host . ';charset=utf8mb4', $this->user, $this->pass);
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `" . $this->dbname . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // إنشاء الجداول
            $this->createTables();
            return true;
        } catch(PDOException $e) {
            return false;
        }
    }

    // إنشاء الجداول
    private function createTables() {
        $tables = [
            // جدول الفئات
            "CREATE TABLE IF NOT EXISTS `categories` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `slug` varchar(255) NOT NULL,
                `description` text,
                `color` varchar(7) DEFAULT '#007bff',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `slug` (`slug`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول المقالات
            "CREATE TABLE IF NOT EXISTS `articles` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(500) NOT NULL,
                `slug` varchar(500) NOT NULL,
                `content` longtext NOT NULL,
                `excerpt` text,
                `image` varchar(255),
                `category_id` int(11),
                `author_id` int(11),
                `status` enum('draft','published','archived') DEFAULT 'draft',
                `is_featured` tinyint(1) DEFAULT 0,
                `is_breaking` tinyint(1) DEFAULT 0,
                `views` int(11) DEFAULT 0,
                `source_url` varchar(500),
                `rss_source_id` int(11),
                `published_at` timestamp NULL DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `slug` (`slug`),
                KEY `category_id` (`category_id`),
                KEY `author_id` (`author_id`),
                KEY `status` (`status`),
                KEY `published_at` (`published_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول المستخدمين
            "CREATE TABLE IF NOT EXISTS `users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(50) NOT NULL,
                `email` varchar(100) NOT NULL,
                `password` varchar(255) NOT NULL,
                `full_name` varchar(100),
                `role` enum('admin','editor','author') DEFAULT 'author',
                `avatar` varchar(255),
                `status` enum('active','inactive') DEFAULT 'active',
                `last_login` timestamp NULL DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `username` (`username`),
                UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول مصادر RSS
            "CREATE TABLE IF NOT EXISTS `rss_sources` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `url` varchar(500) NOT NULL,
                `category_id` int(11),
                `is_active` tinyint(1) DEFAULT 1,
                `last_fetch` timestamp NULL DEFAULT NULL,
                `fetch_interval` int(11) DEFAULT 3600,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `category_id` (`category_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول التعليقات
            "CREATE TABLE IF NOT EXISTS `comments` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `article_id` int(11) NOT NULL,
                `name` varchar(100) NOT NULL,
                `email` varchar(100) NOT NULL,
                `content` text NOT NULL,
                `status` enum('pending','approved','rejected') DEFAULT 'pending',
                `ip_address` varchar(45),
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `article_id` (`article_id`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

            // جدول الإعدادات
            "CREATE TABLE IF NOT EXISTS `settings` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `setting_key` varchar(100) NOT NULL,
                `setting_value` text,
                `setting_type` varchar(50) DEFAULT 'text',
                PRIMARY KEY (`id`),
                UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
        ];

        foreach ($tables as $table) {
            $this->dbh->exec($table);
        }

        // إدراج البيانات الافتراضية
        $this->insertDefaultData();
    }

    // إدراج البيانات الافتراضية
    private function insertDefaultData() {
        // إعدادات افتراضية
        $settings = [
            ['site_name', 'موقع الأخبار العربي', 'text'],
            ['site_description', 'موقع إخباري عربي شامل', 'text'],
            ['site_logo', '', 'text'],
            ['articles_per_page', '10', 'number'],
            ['breaking_news_enabled', '1', 'boolean'],
            ['comments_enabled', '1', 'boolean'],
            ['rss_auto_fetch', '1', 'boolean']
        ];

        foreach ($settings as $setting) {
            $this->query("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
            $this->bind(1, $setting[0]);
            $this->bind(2, $setting[1]);
            $this->bind(3, $setting[2]);
            $this->execute();
        }

        // مستخدم إداري افتراضي
        $this->query("INSERT IGNORE INTO users (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
        $this->bind(1, 'admin');
        $this->bind(2, '<EMAIL>');
        $this->bind(3, password_hash('admin123', PASSWORD_DEFAULT));
        $this->bind(4, 'المدير العام');
        $this->bind(5, 'admin');
        $this->execute();

        // فئات افتراضية
        $categories = [
            ['أخبار عامة', 'general-news', 'آخر الأخبار والأحداث العامة', '#007bff'],
            ['سياسة', 'politics', 'الأخبار السياسية والحكومية', '#dc3545'],
            ['اقتصاد', 'economy', 'الأخبار الاقتصادية والمالية', '#28a745'],
            ['رياضة', 'sports', 'الأخبار الرياضية والمباريات', '#ffc107'],
            ['تكنولوجيا', 'technology', 'أخبار التكنولوجيا والابتكار', '#6f42c1']
        ];

        foreach ($categories as $category) {
            $this->query("INSERT IGNORE INTO categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
            $this->bind(1, $category[0]);
            $this->bind(2, $category[1]);
            $this->bind(3, $category[2]);
            $this->bind(4, $category[3]);
            $this->execute();
        }
    }
}
?>

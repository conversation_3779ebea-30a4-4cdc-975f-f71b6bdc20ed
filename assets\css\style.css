/* تصميم عصري للموقع الإخباري العربي */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --dark-color: #34495e;
    --light-color: #ecf0f1;
    --white-color: #ffffff;
    --text-color: #2c3e50;
    --text-light: #7f8c8d;
    --border-color: #bdc3c7;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-hover: 0 5px 20px rgba(0,0,0,0.15);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif, 'Arial Unicode MS';
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* تحسين الخطوط العربية */
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', 'Arial Unicode MS', sans-serif;
    font-weight: 400;
    letter-spacing: 0.3px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 15px;
}

p {
    margin-bottom: 15px;
    line-height: 1.7;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* الحاوي الرئيسي */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }

[class*="col-"] {
    padding: 0 15px;
}

/* رأس الصفحة */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
    color: var(--white-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: rgba(0,0,0,0.1);
    padding: 8px 0;
    font-size: 14px;
}

.header-main {
    padding: 15px 0;
}

.logo {
    font-size: 28px;
    font-weight: bold;
    color: var(--white-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

.logo img {
    height: 40px;
    width: auto;
}

/* شريط الأخبار العاجلة */
.breaking-news {
    background: var(--accent-color);
    color: var(--white-color);
    padding: 10px 0;
    overflow: hidden;
    position: relative;
}

.breaking-news-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.breaking-label {
    background: rgba(255,255,255,0.2);
    padding: 5px 15px;
    border-radius: var(--border-radius);
    font-weight: bold;
    white-space: nowrap;
}

.breaking-text {
    flex: 1;
    white-space: nowrap;
    animation: scroll-rtl 30s linear infinite;
}

@keyframes scroll-rtl {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

.breaking-news:hover .breaking-text {
    animation-play-state: paused;
}

/* التنقل */
.navbar {
    background: var(--white-color);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 0;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
}

.nav-link:hover,
.nav-link.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
    text-decoration: none;
}

/* البحث */
.search-form {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: auto;
}

.search-input {
    padding: 8px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    width: 250px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-btn {
    padding: 8px 15px;
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* المحتوى الرئيسي */
.main-content {
    padding: 30px 0;
    min-height: calc(100vh - 200px);
}

/* بطاقة المقال */
.article-card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.article-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.category-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: var(--white-color);
    text-decoration: none;
}

.category-badge a {
    color: inherit;
    text-decoration: none;
}

.breaking-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--accent-color);
    color: var(--white-color);
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.article-content {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.article-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.article-title a:hover {
    color: var(--secondary-color);
}

.article-excerpt {
    color: var(--text-light);
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
    flex: 1;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--text-light);
    margin-top: auto;
}

.article-date,
.article-views {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* المقال المميز */
.featured-article {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.featured-article .article-image {
    height: 300px;
}

.featured-article .article-title {
    font-size: 24px;
}

.featured-article .article-excerpt {
    font-size: 16px;
}

/* الشريط الجانبي */
.sidebar {
    padding-right: 30px;
}

.sidebar-widget {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.widget-header {
    background: var(--primary-color);
    color: var(--white-color);
    padding: 15px 20px;
    font-weight: 600;
}

.widget-content {
    padding: 20px;
}

.widget-list {
    list-style: none;
}

.widget-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--light-color);
}

.widget-list li:last-child {
    border-bottom: none;
}

.widget-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.widget-list a:hover {
    color: var(--secondary-color);
}

/* الترقيم */
.pagination {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 30px 0;
    gap: 5px;
}

.page-item {
    display: block;
}

.page-link {
    display: block;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.page-link:hover {
    background: var(--secondary-color);
    color: var(--white-color);
    border-color: var(--secondary-color);
}

.page-item.active .page-link {
    background: var(--secondary-color);
    color: var(--white-color);
    border-color: var(--secondary-color);
}

.page-item.disabled .page-link {
    color: var(--text-light);
    background: var(--light-color);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* تذييل الصفحة */
.footer {
    background: var(--primary-color);
    color: var(--white-color);
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section a {
    color: var(--white-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 20px;
    text-align: center;
    color: var(--text-light);
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    line-height: 1.5;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white-color);
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--text-light);
    color: var(--white-color);
}

.btn-secondary:hover {
    background: var(--text-color);
}

.btn-success {
    background: var(--success-color);
    color: var(--white-color);
}

.btn-danger {
    background: var(--accent-color);
    color: var(--white-color);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
}

.btn-outline:hover {
    background: var(--secondary-color);
    color: var(--white-color);
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control.is-invalid {
    border-color: var(--accent-color);
}

.invalid-feedback {
    color: var(--accent-color);
    font-size: 12px;
    margin-top: 5px;
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .header-main {
        text-align: center;
    }
    
    .navbar-nav {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-link {
        padding: 12px 15px;
        border-bottom: 1px solid var(--light-color);
        border-radius: 0;
    }
    
    .search-form {
        margin: 15px 0;
        justify-content: center;
    }
    
    .search-input {
        width: 200px;
    }
    
    .sidebar {
        padding-right: 0;
        margin-top: 30px;
    }
    
    .article-image {
        height: 180px;
    }
    
    .featured-article .article-image {
        height: 200px;
    }
    
    .breaking-text {
        font-size: 14px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 20px;
    }
    
    .article-title {
        font-size: 16px;
    }
    
    .featured-article .article-title {
        font-size: 20px;
    }
    
    .search-input {
        width: 150px;
    }
    
    .btn {
        padding: 8px 15px;
        font-size: 12px;
    }
}

/* تحسينات إضافية */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }

.overflow-hidden { overflow: hidden; }

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 3px solid var(--light-color);
    border-top: 3px solid var(--secondary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

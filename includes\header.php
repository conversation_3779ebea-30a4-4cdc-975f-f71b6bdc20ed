<?php
// تضمين ملف الإعدادات
require_once dirname(__DIR__) . '/config/config.php';

// الحصول على معلومات الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$page_title = $page_title ?? getSetting('site_name', 'موقع الأخبار العربي');
$page_description = $page_description ?? getSetting('site_description', 'موقع إخباري عربي شامل');
$page_image = $page_image ?? '';
$page_url = $page_url ?? '';

// الحصول على الفئات للقائمة
$categories = getCategories();

// الحصول على الأخبار العاجلة
$breaking_news = getArticles(5, 0, null, 'published', null, 1);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <?php echo generateMetaTags($page_title, $page_description, $page_image, $page_url); ?>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- RSS Feed -->
    <link rel="alternate" type="application/rss+xml" title="<?php echo getSetting('site_name'); ?> RSS" href="<?php echo SITE_URL; ?>/rss.php">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="<?php echo $current_page; ?>-page">
    
    <!-- شريط الأخبار العاجلة -->
    <?php if (getSetting('breaking_news_enabled', 1) && !empty($breaking_news)): ?>
    <div class="breaking-news">
        <div class="container">
            <div class="breaking-news-content">
                <span class="breaking-label">
                    <i class="fas fa-bolt"></i>
                    عاجل
                </span>
                <div class="breaking-text">
                    <?php foreach ($breaking_news as $index => $news): ?>
                        <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $news['slug']; ?>">
                            <?php echo htmlspecialchars($news['title']); ?>
                        </a>
                        <?php if ($index < count($breaking_news) - 1): ?>
                            <span class="separator"> • </span>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- رأس الصفحة -->
    <header class="header">
        <!-- الشريط العلوي -->
        <div class="header-top">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-6">
                        <div class="header-info">
                            <span class="date">
                                <i class="fas fa-calendar-alt"></i>
                                <?php echo date('l, d F Y', strtotime('now')); ?>
                            </span>
                        </div>
                    </div>
                    <div class="col-6 text-left">
                        <div class="header-social">
                            <a href="#" class="social-link" title="فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" title="تويتر">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="إنستغرام">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="يوتيوب">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <button class="dark-mode-toggle" onclick="toggleDarkMode()" title="الوضع الليلي">
                                <i class="fas fa-moon"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الشريط الرئيسي -->
        <div class="header-main">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <?php $logo = getSetting('site_logo'); ?>
                            <?php if ($logo): ?>
                                <img src="<?php echo SITE_URL . '/' . $logo; ?>" alt="<?php echo getSetting('site_name'); ?>">
                            <?php else: ?>
                                <i class="fas fa-newspaper"></i>
                            <?php endif; ?>
                            <span><?php echo getSetting('site_name', 'موقع الأخبار'); ?></span>
                        </a>
                    </div>
                    <div class="col-6">
                        <div class="header-center">
                            <p class="site-description"><?php echo getSetting('site_description', 'موقع إخباري عربي شامل'); ?></p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="header-search">
                            <form class="search-form" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                                <div class="search-input-group">
                                    <input type="text" name="q" class="search-input" placeholder="ابحث في الأخبار..." 
                                           value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>" autocomplete="off">
                                    <button type="submit" class="search-btn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div class="search-results" style="display: none;"></div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-content">
                <button class="mobile-menu-toggle d-lg-none">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>" class="nav-link <?php echo $current_page == 'index' ? 'active' : ''; ?>">
                            <i class="fas fa-home"></i>
                            الرئيسية
                        </a>
                    </li>
                    
                    <?php foreach ($categories as $category): ?>
                    <li class="nav-item">
                        <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" 
                           class="nav-link <?php echo (isset($_GET['slug']) && $_GET['slug'] == $category['slug']) ? 'active' : ''; ?>">
                            <?php echo htmlspecialchars($category['name']); ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                    
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            المزيد
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a href="<?php echo SITE_URL; ?>/about.php" class="dropdown-link">من نحن</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/contact.php" class="dropdown-link">اتصل بنا</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/privacy.php" class="dropdown-link">سياسة الخصوصية</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/rss.php" class="dropdown-link">RSS</a></li>
                        </ul>
                    </li>
                </ul>
                
                <div class="navbar-actions">
                    <?php if (isLoggedIn()): ?>
                        <a href="<?php echo SITE_URL; ?>/admin/" class="btn btn-outline">
                            <i class="fas fa-cog"></i>
                            لوحة التحكم
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- شريط التقدم -->
    <div class="reading-progress"></div>
    
    <!-- بداية المحتوى الرئيسي -->
    <main class="main-content">

<style>
/* تحسينات إضافية للرأس */
.header-top {
    font-size: 13px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.header-info .date {
    color: rgba(255,255,255,0.9);
}

.header-social {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: flex-end;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: rgba(255,255,255,0.1);
    color: var(--white-color);
    border-radius: 50%;
    transition: var(--transition);
    font-size: 12px;
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.dark-mode-toggle {
    background: rgba(255,255,255,0.1);
    border: none;
    color: var(--white-color);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
}

.dark-mode-toggle:hover {
    background: var(--warning-color);
}

.site-description {
    text-align: center;
    color: rgba(255,255,255,0.8);
    font-size: 14px;
    margin: 0;
}

.search-input-group {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.search-btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.search-results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    padding: 15px;
    border-bottom: 1px solid var(--light-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: var(--light-color);
}

.result-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.result-excerpt {
    font-size: 13px;
    color: var(--text-light);
    margin-bottom: 5px;
}

.result-meta {
    font-size: 11px;
    color: var(--text-light);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-color);
    margin: 3px 0;
    transition: var(--transition);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-link:hover {
    background: var(--light-color);
    color: var(--secondary-color);
}

.reading-progress {
    position: fixed;
    top: 0;
    right: 0;
    width: 0%;
    height: 3px;
    background: var(--secondary-color);
    z-index: 9999;
    transition: width 0.3s ease;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }
    
    .navbar-nav {
        position: absolute;
        top: 100%;
        right: 0;
        left: 0;
        background: var(--white-color);
        flex-direction: column;
        box-shadow: var(--shadow);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }
    
    .navbar-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-link {
        padding: 15px 20px;
        border-bottom: 1px solid var(--light-color);
    }
    
    .header-search {
        margin-top: 15px;
    }
    
    .search-input {
        width: 100%;
    }
}
</style>

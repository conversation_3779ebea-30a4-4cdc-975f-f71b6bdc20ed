<?php
// وظائف مساعدة للموقع

// وظائف المقالات
function getArticles($limit = 10, $offset = 0, $category_id = null, $status = 'published', $featured = null, $breaking = null) {
    global $db;
    
    $where_conditions = ["a.status = ?"];
    $params = [$status];
    
    if ($category_id) {
        $where_conditions[] = "a.category_id = ?";
        $params[] = $category_id;
    }
    
    if ($featured !== null) {
        $where_conditions[] = "a.is_featured = ?";
        $params[] = $featured;
    }
    
    if ($breaking !== null) {
        $where_conditions[] = "a.is_breaking = ?";
        $params[] = $breaking;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $query = "SELECT a.*, c.name as category_name, c.slug as category_slug, c.color as category_color,
                     u.full_name as author_name
              FROM articles a 
              LEFT JOIN categories c ON a.category_id = c.id 
              LEFT JOIN users u ON a.author_id = u.id 
              WHERE $where_clause 
              ORDER BY a.published_at DESC, a.created_at DESC 
              LIMIT ? OFFSET ?";
    
    $db->query($query);
    
    foreach ($params as $index => $param) {
        $db->bind($index + 1, $param);
    }
    $db->bind(count($params) + 1, $limit);
    $db->bind(count($params) + 2, $offset);
    
    return $db->resultset();
}

function getArticleBySlug($slug) {
    global $db;
    
    $db->query("SELECT a.*, c.name as category_name, c.slug as category_slug, c.color as category_color,
                       u.full_name as author_name
                FROM articles a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE a.slug = ? AND a.status = 'published'");
    $db->bind(1, $slug);
    
    return $db->single();
}

function getArticleById($id) {
    global $db;
    
    $db->query("SELECT a.*, c.name as category_name, c.slug as category_slug,
                       u.full_name as author_name
                FROM articles a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE a.id = ?");
    $db->bind(1, $id);
    
    return $db->single();
}

function incrementArticleViews($id) {
    global $db;
    
    $db->query("UPDATE articles SET views = views + 1 WHERE id = ?");
    $db->bind(1, $id);
    return $db->execute();
}

function getRelatedArticles($article_id, $category_id, $limit = 5) {
    global $db;
    
    $db->query("SELECT a.*, c.name as category_name, c.slug as category_slug
                FROM articles a 
                LEFT JOIN categories c ON a.category_id = c.id 
                WHERE a.category_id = ? AND a.id != ? AND a.status = 'published'
                ORDER BY a.published_at DESC 
                LIMIT ?");
    $db->bind(1, $category_id);
    $db->bind(2, $article_id);
    $db->bind(3, $limit);
    
    return $db->resultset();
}

function searchArticles($query, $limit = 10, $offset = 0) {
    global $db;
    
    $search_query = "%$query%";
    
    $db->query("SELECT a.*, c.name as category_name, c.slug as category_slug,
                       u.full_name as author_name
                FROM articles a 
                LEFT JOIN categories c ON a.category_id = c.id 
                LEFT JOIN users u ON a.author_id = u.id 
                WHERE (a.title LIKE ? OR a.content LIKE ? OR a.excerpt LIKE ?) 
                AND a.status = 'published'
                ORDER BY a.published_at DESC 
                LIMIT ? OFFSET ?");
    $db->bind(1, $search_query);
    $db->bind(2, $search_query);
    $db->bind(3, $search_query);
    $db->bind(4, $limit);
    $db->bind(5, $offset);
    
    return $db->resultset();
}

function countArticles($category_id = null, $status = 'published', $search = null) {
    global $db;
    
    $where_conditions = ["status = ?"];
    $params = [$status];
    
    if ($category_id) {
        $where_conditions[] = "category_id = ?";
        $params[] = $category_id;
    }
    
    if ($search) {
        $where_conditions[] = "(title LIKE ? OR content LIKE ? OR excerpt LIKE ?)";
        $search_query = "%$search%";
        $params[] = $search_query;
        $params[] = $search_query;
        $params[] = $search_query;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $db->query("SELECT COUNT(*) as total FROM articles WHERE $where_clause");
    
    foreach ($params as $index => $param) {
        $db->bind($index + 1, $param);
    }
    
    $result = $db->single();
    return $result['total'];
}

// وظائف الفئات
function getCategories($active_only = true) {
    global $db;
    
    $query = "SELECT * FROM categories";
    if ($active_only) {
        $query .= " WHERE id IN (SELECT DISTINCT category_id FROM articles WHERE status = 'published' AND category_id IS NOT NULL)";
    }
    $query .= " ORDER BY name";
    
    $db->query($query);
    return $db->resultset();
}

function getCategoryBySlug($slug) {
    global $db;
    
    $db->query("SELECT * FROM categories WHERE slug = ?");
    $db->bind(1, $slug);
    
    return $db->single();
}

function getCategoryById($id) {
    global $db;
    
    $db->query("SELECT * FROM categories WHERE id = ?");
    $db->bind(1, $id);
    
    return $db->single();
}

// وظائف التعليقات
function getComments($article_id, $status = 'approved') {
    global $db;
    
    $db->query("SELECT * FROM comments WHERE article_id = ? AND status = ? ORDER BY created_at ASC");
    $db->bind(1, $article_id);
    $db->bind(2, $status);
    
    return $db->resultset();
}

function addComment($article_id, $name, $email, $content, $ip_address) {
    global $db;
    
    $db->query("INSERT INTO comments (article_id, name, email, content, ip_address) VALUES (?, ?, ?, ?, ?)");
    $db->bind(1, $article_id);
    $db->bind(2, sanitize($name));
    $db->bind(3, sanitize($email));
    $db->bind(4, sanitize($content));
    $db->bind(5, $ip_address);
    
    return $db->execute();
}

function countComments($article_id, $status = 'approved') {
    global $db;
    
    $db->query("SELECT COUNT(*) as total FROM comments WHERE article_id = ? AND status = ?");
    $db->bind(1, $article_id);
    $db->bind(2, $status);
    
    $result = $db->single();
    return $result['total'];
}

// وظائف المستخدمين
function getUserById($id) {
    global $db;
    
    $db->query("SELECT * FROM users WHERE id = ?");
    $db->bind(1, $id);
    
    return $db->single();
}

function getUserByUsername($username) {
    global $db;
    
    $db->query("SELECT * FROM users WHERE username = ?");
    $db->bind(1, $username);
    
    return $db->single();
}

function authenticateUser($username, $password) {
    $user = getUserByUsername($username);
    
    if ($user && password_verify($password, $user['password'])) {
        // تحديث آخر تسجيل دخول
        global $db;
        $db->query("UPDATE users SET last_login = NOW() WHERE id = ?");
        $db->bind(1, $user['id']);
        $db->execute();
        
        return $user;
    }
    
    return false;
}

// وظائف RSS
function getRssSources($active_only = true) {
    global $db;
    
    $query = "SELECT r.*, c.name as category_name FROM rss_sources r 
              LEFT JOIN categories c ON r.category_id = c.id";
    if ($active_only) {
        $query .= " WHERE r.is_active = 1";
    }
    $query .= " ORDER BY r.name";
    
    $db->query($query);
    return $db->resultset();
}

// وظائف الإحصائيات
function getStats() {
    global $db;
    
    $stats = [];
    
    // عدد المقالات
    $db->query("SELECT COUNT(*) as total FROM articles WHERE status = 'published'");
    $result = $db->single();
    $stats['articles'] = $result['total'];
    
    // عدد الفئات
    $db->query("SELECT COUNT(*) as total FROM categories");
    $result = $db->single();
    $stats['categories'] = $result['total'];
    
    // عدد التعليقات
    $db->query("SELECT COUNT(*) as total FROM comments WHERE status = 'approved'");
    $result = $db->single();
    $stats['comments'] = $result['total'];
    
    // عدد المشاهدات
    $db->query("SELECT SUM(views) as total FROM articles WHERE status = 'published'");
    $result = $db->single();
    $stats['views'] = $result['total'] ?: 0;
    
    return $stats;
}

// وظائف مساعدة للعرض
function renderArticleCard($article, $show_excerpt = true, $show_category = true) {
    $image_url = $article['image'] ? SITE_URL . '/' . $article['image'] : SITE_URL . '/assets/images/default-article.jpg';
    $article_url = SITE_URL . '/article.php?slug=' . $article['slug'];
    $category_url = SITE_URL . '/category.php?slug=' . $article['category_slug'];
    
    $html = '<div class="article-card">';
    $html .= '<div class="article-image">';
    $html .= '<a href="' . $article_url . '">';
    $html .= '<img src="' . $image_url . '" alt="' . htmlspecialchars($article['title']) . '" loading="lazy">';
    $html .= '</a>';
    
    if ($show_category && $article['category_name']) {
        $html .= '<span class="category-badge" style="background-color: ' . $article['category_color'] . '">';
        $html .= '<a href="' . $category_url . '">' . htmlspecialchars($article['category_name']) . '</a>';
        $html .= '</span>';
    }
    
    if ($article['is_breaking']) {
        $html .= '<span class="breaking-badge">عاجل</span>';
    }
    
    $html .= '</div>';
    
    $html .= '<div class="article-content">';
    $html .= '<h3 class="article-title">';
    $html .= '<a href="' . $article_url . '">' . htmlspecialchars($article['title']) . '</a>';
    $html .= '</h3>';
    
    if ($show_excerpt && $article['excerpt']) {
        $html .= '<p class="article-excerpt">' . htmlspecialchars(truncateText($article['excerpt'], 120)) . '</p>';
    }
    
    $html .= '<div class="article-meta">';
    $html .= '<span class="article-date">' . timeAgo($article['published_at'] ?: $article['created_at']) . '</span>';
    if ($article['views'] > 0) {
        $html .= '<span class="article-views">' . number_format($article['views']) . ' مشاهدة</span>';
    }
    $html .= '</div>';
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

function renderBreadcrumb($items) {
    $html = '<nav aria-label="breadcrumb">';
    $html .= '<ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        $is_last = ($index === count($items) - 1);
        
        if ($is_last) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($item['title']) . '</li>';
        } else {
            $html .= '<li class="breadcrumb-item">';
            $html .= '<a href="' . $item['url'] . '">' . htmlspecialchars($item['title']) . '</a>';
            $html .= '</li>';
        }
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}
?>

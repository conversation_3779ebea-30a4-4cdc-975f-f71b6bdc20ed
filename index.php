<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// إعدادات الصفحة
$page_title = getSetting('site_name', 'موقع الأخبار العربي') . ' - ' . getSetting('site_description', 'موقع إخباري عربي شامل');
$page_description = getSetting('site_description', 'موقع إخباري عربي شامل يقدم آخر الأخبار والتحليلات من جميع أنحاء العالم العربي والعالم');

// الحصول على المقالات
$articles_per_page = (int)getSetting('articles_per_page', 10);
$current_page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($current_page - 1) * $articles_per_page;

// المقالات المميزة
$featured_articles = getArticles(3, 0, null, 'published', 1);

// المقالات العادية
$regular_articles = getArticles($articles_per_page, $offset, null, 'published', 0);

// إجمالي المقالات للترقيم
$total_articles = countArticles(null, 'published');
$total_pages = ceil($total_articles / $articles_per_page);

// الأخبار العاجلة
$breaking_news = getArticles(5, 0, null, 'published', null, 1);

// أحدث المقالات حسب الفئة
$categories_with_articles = [];
$categories = getCategories();
foreach (array_slice($categories, 0, 4) as $category) {
    $category_articles = getArticles(4, 0, $category['id'], 'published');
    if (!empty($category_articles)) {
        $categories_with_articles[] = [
            'category' => $category,
            'articles' => $category_articles
        ];
    }
}

// تضمين الرأس
include 'includes/header.php';
?>

<div class="container">
    <div class="row">
        <!-- المحتوى الرئيسي -->
        <div class="col-lg-8 col-md-12">
            
            <!-- المقالات المميزة -->
            <?php if (!empty($featured_articles)): ?>
            <section class="featured-section mb-4">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        المقالات المميزة
                    </h2>
                </div>
                
                <div class="featured-articles">
                    <?php $main_featured = array_shift($featured_articles); ?>
                    
                    <!-- المقال المميز الرئيسي -->
                    <div class="main-featured">
                        <div class="featured-article large">
                            <div class="article-image">
                                <?php 
                                $image_url = $main_featured['image'] ? SITE_URL . '/' . $main_featured['image'] : SITE_URL . '/assets/images/default-article.jpg';
                                ?>
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $main_featured['slug']; ?>">
                                    <img src="<?php echo $image_url; ?>" alt="<?php echo htmlspecialchars($main_featured['title']); ?>" loading="lazy">
                                </a>
                                
                                <?php if ($main_featured['category_name']): ?>
                                <span class="category-badge" style="background-color: <?php echo $main_featured['category_color']; ?>">
                                    <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $main_featured['category_slug']; ?>">
                                        <?php echo htmlspecialchars($main_featured['category_name']); ?>
                                    </a>
                                </span>
                                <?php endif; ?>
                                
                                <?php if ($main_featured['is_breaking']): ?>
                                <span class="breaking-badge">عاجل</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="article-content">
                                <h3 class="article-title">
                                    <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $main_featured['slug']; ?>">
                                        <?php echo htmlspecialchars($main_featured['title']); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($main_featured['excerpt']): ?>
                                <p class="article-excerpt">
                                    <?php echo htmlspecialchars(truncateText($main_featured['excerpt'], 200)); ?>
                                </p>
                                <?php endif; ?>
                                
                                <div class="article-meta">
                                    <span class="article-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo timeAgo($main_featured['published_at'] ?: $main_featured['created_at']); ?>
                                    </span>
                                    <?php if ($main_featured['views'] > 0): ?>
                                    <span class="article-views">
                                        <i class="fas fa-eye"></i>
                                        <?php echo number_format($main_featured['views']); ?> مشاهدة
                                    </span>
                                    <?php endif; ?>
                                    <?php if ($main_featured['author_name']): ?>
                                    <span class="article-author">
                                        <i class="fas fa-user"></i>
                                        <?php echo htmlspecialchars($main_featured['author_name']); ?>
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- المقالات المميزة الفرعية -->
                    <?php if (!empty($featured_articles)): ?>
                    <div class="sub-featured">
                        <div class="row">
                            <?php foreach ($featured_articles as $article): ?>
                            <div class="col-md-6">
                                <?php echo renderArticleCard($article, true, true); ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </section>
            <?php endif; ?>
            
            <!-- أحدث الأخبار -->
            <section class="latest-news mb-4">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-newspaper"></i>
                        أحدث الأخبار
                    </h2>
                </div>
                
                <?php if (!empty($regular_articles)): ?>
                <div class="articles-grid">
                    <div class="row">
                        <?php foreach ($regular_articles as $article): ?>
                        <div class="col-lg-6 col-md-6 mb-4">
                            <?php echo renderArticleCard($article, true, true); ?>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination-wrapper">
                    <?php echo getPagination($current_page, $total_pages, SITE_URL); ?>
                </div>
                <?php endif; ?>
                
                <?php else: ?>
                <div class="no-articles">
                    <div class="no-content-message">
                        <i class="fas fa-newspaper"></i>
                        <h3>لا توجد مقالات متاحة حالياً</h3>
                        <p>نعمل على إضافة محتوى جديد قريباً</p>
                    </div>
                </div>
                <?php endif; ?>
            </section>
            
            <!-- أقسام الأخبار حسب الفئة -->
            <?php if (!empty($categories_with_articles)): ?>
            <section class="category-sections">
                <?php foreach ($categories_with_articles as $section): ?>
                <div class="category-section mb-5">
                    <div class="section-header">
                        <h2 class="section-title" style="color: <?php echo $section['category']['color']; ?>">
                            <span class="category-icon" style="background-color: <?php echo $section['category']['color']; ?>"></span>
                            <?php echo htmlspecialchars($section['category']['name']); ?>
                        </h2>
                        <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $section['category']['slug']; ?>" class="view-all-link">
                            عرض الكل
                            <i class="fas fa-arrow-left"></i>
                        </a>
                    </div>
                    
                    <div class="category-articles">
                        <div class="row">
                            <?php foreach ($section['articles'] as $index => $article): ?>
                            <div class="col-lg-<?php echo $index === 0 ? '6' : '3'; ?> col-md-6 mb-3">
                                <?php echo renderArticleCard($article, $index === 0, true); ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </section>
            <?php endif; ?>
            
        </div>
        
        <!-- الشريط الجانبي -->
        <div class="col-lg-4 col-md-12">
            <?php include 'includes/sidebar.php'; ?>
        </div>
    </div>
</div>

<style>
/* تنسيقات الصفحة الرئيسية */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid var(--secondary-color);
}

.section-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: var(--secondary-color);
}

.view-all-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: var(--transition);
}

.view-all-link:hover {
    color: var(--primary-color);
    transform: translateX(-3px);
}

/* المقالات المميزة */
.featured-section {
    margin-bottom: 40px;
}

.featured-articles {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.featured-article.large {
    height: 100%;
}

.featured-article.large .article-image {
    height: 300px;
}

.featured-article.large .article-title {
    font-size: 28px;
    line-height: 1.3;
    margin-bottom: 15px;
}

.featured-article.large .article-excerpt {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.featured-article.large .article-meta {
    font-size: 14px;
    gap: 15px;
}

.sub-featured .article-card {
    margin-bottom: 20px;
}

.sub-featured .article-image {
    height: 150px;
}

.sub-featured .article-title {
    font-size: 16px;
}

/* شبكة المقالات */
.articles-grid {
    margin-bottom: 30px;
}

/* أقسام الفئات */
.category-section {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.category-icon {
    display: inline-block;
    width: 4px;
    height: 20px;
    border-radius: 2px;
    margin-left: 5px;
}

.category-articles .article-card {
    height: 100%;
}

/* رسالة عدم وجود محتوى */
.no-articles {
    text-align: center;
    padding: 60px 20px;
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.no-content-message i {
    font-size: 64px;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-content-message h3 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.no-content-message p {
    color: var(--text-light);
    margin: 0;
}

/* تحسينات الترقيم */
.pagination-wrapper {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

/* تحسينات بطاقة المقال للصفحة الرئيسية */
.article-card {
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.article-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-color: var(--secondary-color);
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    font-size: 13px;
    color: var(--text-light);
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.article-meta i {
    font-size: 12px;
}

/* التصميم المتجاوب */
@media (max-width: 992px) {
    .featured-articles {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .featured-article.large .article-image {
        height: 250px;
    }
    
    .featured-article.large .article-title {
        font-size: 24px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .view-all-link {
        align-self: flex-end;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 20px;
    }
    
    .featured-article.large .article-title {
        font-size: 20px;
    }
    
    .featured-article.large .article-excerpt {
        font-size: 14px;
    }
    
    .category-section {
        padding: 20px 15px;
    }
    
    .articles-grid .col-lg-6 {
        margin-bottom: 20px;
    }
    
    .category-articles .col-lg-3,
    .category-articles .col-lg-6 {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .featured-article.large .article-image {
        height: 200px;
    }
    
    .sub-featured .article-image {
        height: 120px;
    }
    
    .article-meta {
        gap: 10px;
        font-size: 12px;
    }
    
    .section-header {
        margin-bottom: 20px;
        padding-bottom: 10px;
    }
    
    .no-content-message {
        padding: 40px 15px;
    }
    
    .no-content-message i {
        font-size: 48px;
    }
}

/* تحسينات إضافية للأداء */
.article-image img {
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* تحسينات إمكانية الوصول */
.article-card:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.view-all-link:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .pagination-wrapper,
    .view-all-link {
        display: none;
    }
    
    .article-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .featured-articles {
        grid-template-columns: 1fr;
    }
}
</style>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>

<?php
// ملف اختبار أساسي - احذفه بعد حل المشكلة!

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>اختبار أساسي للموقع</h1>";

// اختبار PHP الأساسي
echo "<h2>✅ PHP يعمل بشكل صحيح!</h2>";
echo "<p>الوقت الحالي: " . date('Y-m-d H:i:s') . "</p>";

// اختبار تضمين الملفات
echo "<h3>اختبار تضمين الملفات:</h3>";

// اختبار ملف التكوين
if (file_exists('config/config.php')) {
    echo "✅ ملف config.php موجود<br>";
    
    try {
        // محاولة تضمين ملف التكوين بدون تنفيذ الكود
        $config_content = file_get_contents('config/config.php');
        if ($config_content !== false) {
            echo "✅ يمكن قراءة ملف config.php<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في قراءة config.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف config.php غير موجود<br>";
}

// اختبار المجلدات
$folders = ['config', 'includes', 'assets', 'uploads', 'admin'];
echo "<h3>اختبار المجلدات:</h3>";
foreach ($folders as $folder) {
    if (is_dir($folder)) {
        echo "✅ مجلد $folder موجود<br>";
    } else {
        echo "❌ مجلد $folder غير موجود<br>";
    }
}

// اختبار الملفات الأساسية
$files = [
    'index.php',
    'article.php', 
    'category.php',
    'search.php',
    'includes/header.php',
    'includes/footer.php'
];

echo "<h3>اختبار الملفات الأساسية:</h3>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ ملف $file موجود<br>";
    } else {
        echo "❌ ملف $file غير موجود<br>";
    }
}

// اختبار بسيط لقاعدة البيانات (بدون تضمين config.php)
echo "<h3>اختبار قاعدة البيانات:</h3>";
echo "<p>يرجى التحقق من إعدادات قاعدة البيانات في config/config.php</p>";

// معلومات مفيدة للتشخيص
echo "<h3>معلومات النظام:</h3>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "مجلد العمل: " . getcwd() . "<br>";
echo "مجلد الجذر: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

echo "<hr>";
echo "<p><strong>إذا رأيت هذه الرسالة، فإن PHP يعمل بشكل صحيح!</strong></p>";
echo "<p>الآن يمكنك المتابعة لحل المشاكل الأخرى.</p>";
?>

/* تنسيقات لوحة التحكم الإدارية */

/* إعدادات عامة للوحة التحكم */
.admin-layout {
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
}

/* رأس المحتوى */
.content-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title i {
    color: var(--secondary-color);
}

.page-subtitle {
    color: var(--text-light);
    font-size: 16px;
    margin: 0;
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--white-color);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card.articles::before { background: linear-gradient(90deg, #3498db, #2980b9); }
.stat-card.categories::before { background: linear-gradient(90deg, #27ae60, #229954); }
.stat-card.comments::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
.stat-card.views::before { background: linear-gradient(90deg, #9b59b6, #8e44ad); }
.stat-card.users::before { background: linear-gradient(90deg, #34495e, #2c3e50); }
.stat-card.rss::before { background: linear-gradient(90deg, #e74c3c, #c0392b); }

.stat-card {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--white-color);
    flex-shrink: 0;
}

.stat-card.articles .stat-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
.stat-card.categories .stat-icon { background: linear-gradient(135deg, #27ae60, #229954); }
.stat-card.comments .stat-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
.stat-card.views .stat-icon { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
.stat-card.users .stat-icon { background: linear-gradient(135deg, #34495e, #2c3e50); }
.stat-card.rss .stat-icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 16px;
    color: var(--text-color);
    font-weight: 500;
    margin-bottom: 5px;
}

.stat-extra {
    font-size: 12px;
    color: var(--text-light);
}

.stat-action {
    margin-top: 15px;
}

.stat-action .btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* أقسام لوحة التحكم */
.dashboard-sections {
    margin-top: 40px;
}

.dashboard-widget {
    background: var(--white-color);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: 1px solid var(--border-color);
    margin-bottom: 30px;
    overflow: hidden;
}

.widget-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-title i {
    color: var(--secondary-color);
}

.widget-action {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

.widget-action:hover {
    color: var(--primary-color);
}

.widget-content {
    padding: 25px;
}

/* قائمة المقالات */
.articles-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.article-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.article-item:hover {
    background: #e3f2fd;
    transform: translateX(-3px);
}

.article-info {
    flex: 1;
}

.article-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.4;
}

.article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.article-title a:hover {
    color: var(--secondary-color);
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 12px;
    color: var(--text-light);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.meta-item i {
    font-size: 11px;
}

.article-actions {
    display: flex;
    gap: 5px;
}

/* قائمة التعليقات */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.comment-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.comment-item:hover {
    background: #fff3e0;
    transform: translateX(-3px);
}

.comment-info {
    flex: 1;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.comment-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

.comment-content {
    font-size: 14px;
    color: var(--text-color);
    line-height: 1.5;
    margin-bottom: 8px;
}

.comment-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 11px;
    color: var(--text-light);
}

.comment-meta a {
    color: var(--secondary-color);
    text-decoration: none;
}

.comment-meta a:hover {
    text-decoration: underline;
}

.comment-actions {
    display: flex;
    gap: 5px;
}

/* جدول المقالات الشائعة */
.popular-articles-table {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.table th {
    background: var(--light-color);
    padding: 12px 15px;
    text-align: right;
    font-weight: 600;
    color: var(--primary-color);
    border-bottom: 2px solid var(--border-color);
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--light-color);
}

.article-cell strong {
    display: block;
    margin-bottom: 3px;
    color: var(--text-color);
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.badge-secondary {
    background: var(--text-light);
    color: var(--white-color);
}

.btn-group {
    display: flex;
    gap: 5px;
}

/* الإجراءات السريعة */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px 20px;
    background: var(--light-color);
    color: var(--text-color);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.quick-action-btn:hover {
    background: var(--secondary-color);
    color: var(--white-color);
    transform: translateX(-3px);
}

.quick-action-btn i {
    width: 20px;
    text-align: center;
}

/* معلومات النظام */
.system-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: var(--text-color);
}

.info-value {
    color: var(--text-light);
    font-size: 14px;
}

/* الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-light);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
    color: var(--border-color);
}

.empty-state p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* أزرار محسنة */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    line-height: 1.5;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-lg {
    padding: 15px 30px;
    font-size: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), #2980b9);
    color: var(--white-color);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, var(--secondary-color));
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #229954);
    color: var(--white-color);
    border-color: var(--success-color);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    color: var(--white-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background: linear-gradient(135deg, var(--accent-color), #c0392b);
    color: var(--white-color);
    border-color: var(--accent-color);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: var(--white-color);
    border-color: #17a2b8;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--text-light), #5a6268);
    color: var(--white-color);
    border-color: var(--text-light);
}

.btn-outline-primary {
    background: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary:hover {
    background: var(--secondary-color);
    color: var(--white-color);
}

.btn-outline-info {
    background: transparent;
    color: #17a2b8;
    border-color: #17a2b8;
}

.btn-outline-info:hover {
    background: #17a2b8;
    color: var(--white-color);
}

/* التصميم المتجاوب */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .stat-number {
        font-size: 28px;
    }
    
    .widget-header {
        padding: 15px 20px;
    }
    
    .widget-content {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .content-header {
        margin-bottom: 20px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .article-item,
    .comment-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .article-actions,
    .comment-actions {
        align-self: flex-end;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th,
    .table td {
        padding: 8px 10px;
    }
    
    .quick-action-btn {
        padding: 12px 15px;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .widget-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .widget-title {
        font-size: 16px;
    }
    
    .stat-number {
        font-size: 24px;
    }
    
    .article-meta,
    .comment-meta {
        flex-direction: column;
        gap: 5px;
    }
    
    .btn-group {
        flex-direction: column;
    }
}

/* تحسينات إضافية */
.highlight {
    background: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.border-radius-lg {
    border-radius: 12px;
}

.shadow-sm {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.shadow-lg {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* تحسينات الأداء */
.stat-card,
.dashboard-widget,
.article-item,
.comment-item {
    will-change: transform;
}

/* تحسينات إمكانية الوصول */
.btn:focus,
.quick-action-btn:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.table th {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* طباعة */
@media print {
    .stat-card,
    .dashboard-widget {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .btn,
    .quick-action-btn {
        display: none;
    }
}

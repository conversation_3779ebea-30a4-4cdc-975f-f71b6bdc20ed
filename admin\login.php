<?php
// تضمين ملف الإعدادات
require_once dirname(__DIR__) . '/config/config.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    redirect(SITE_URL . '/admin/');
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $user = authenticateUser($username, $password);
        
        if ($user) {
            // إنشاء الجلسة
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['full_name'] = $user['full_name'];
            
            // تذكرني (اختياري - يمكن تطويره لاحقاً)
            if ($remember_me) {
                // يمكن إضافة نظام تذكر المستخدم هنا
            }
            
            $success_message = 'تم تسجيل الدخول بنجاح';
            
            // إعادة توجيه بعد ثانيتين
            header("refresh:2;url=" . SITE_URL . "/admin/");
        } else {
            $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}

// إعدادات الصفحة
$page_title = 'تسجيل الدخول - لوحة التحكم';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $page_title; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
</head>
<body class="login-page">
    
    <div class="login-container">
        <div class="login-wrapper">
            <!-- شعار الموقع -->
            <div class="login-header">
                <div class="logo-section">
                    <a href="<?php echo SITE_URL; ?>" class="logo">
                        <?php $logo = getSetting('site_logo'); ?>
                        <?php if ($logo): ?>
                            <img src="<?php echo SITE_URL . '/' . $logo; ?>" alt="<?php echo getSetting('site_name'); ?>">
                        <?php else: ?>
                            <i class="fas fa-newspaper"></i>
                        <?php endif; ?>
                        <span><?php echo getSetting('site_name', 'موقع الأخبار'); ?></span>
                    </a>
                </div>
                <h1 class="login-title">لوحة التحكم</h1>
                <p class="login-subtitle">قم بتسجيل الدخول للوصول إلى لوحة الإدارة</p>
            </div>
            
            <!-- نموذج تسجيل الدخول -->
            <div class="login-form-container">
                <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $success_message; ?>
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        جاري إعادة التوجيه...
                    </div>
                </div>
                <?php endif; ?>
                
                <form class="login-form" method="POST" action="">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="username" name="username" class="form-control" 
                               placeholder="أدخل اسم المستخدم" 
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                               required autocomplete="username">
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="password-input-group">
                            <input type="password" id="password" name="password" class="form-control" 
                                   placeholder="أدخل كلمة المرور" required autocomplete="current-password">
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="password-icon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group form-check">
                        <input type="checkbox" id="remember_me" name="remember_me" class="form-check-input">
                        <label for="remember_me" class="form-check-label">
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" name="login" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>
            </div>
            
            <!-- روابط إضافية -->
            <div class="login-footer">
                <div class="login-links">
                    <a href="<?php echo SITE_URL; ?>" class="back-to-site">
                        <i class="fas fa-arrow-right"></i>
                        العودة للموقع
                    </a>
                </div>
                
                <div class="login-info">
                    <p>للحصول على حساب إداري، يرجى التواصل مع مدير الموقع</p>
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="system-info">
            <div class="info-item">
                <i class="fas fa-shield-alt"></i>
                <span>نظام آمن ومحمي</span>
            </div>
            <div class="info-item">
                <i class="fas fa-mobile-alt"></i>
                <span>متوافق مع جميع الأجهزة</span>
            </div>
            <div class="info-item">
                <i class="fas fa-clock"></i>
                <span>متاح 24/7</span>
            </div>
        </div>
    </div>

<style>
/* تنسيقات صفحة تسجيل الدخول */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-family: 'Cairo', sans-serif;
}

.login-container {
    width: 100%;
    max-width: 450px;
    position: relative;
}

.login-wrapper {
    background: var(--white-color);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 40px;
    margin-bottom: 30px;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

/* رأس تسجيل الدخول */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-section .logo {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    text-decoration: none;
    margin-bottom: 20px;
}

.logo-section .logo img {
    height: 40px;
    width: auto;
}

.logo-section .logo i {
    font-size: 32px;
    color: var(--secondary-color);
}

.login-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.login-subtitle {
    color: var(--text-light);
    font-size: 14px;
    margin: 0;
}

/* نموذج تسجيل الدخول */
.login-form-container {
    margin-bottom: 30px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form .form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
    font-size: 14px;
}

.login-form .form-label i {
    color: var(--secondary-color);
    width: 16px;
}

.login-form .form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 14px;
    transition: var(--transition);
    background: var(--white-color);
}

.login-form .form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--secondary-color);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-check-input {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
}

.form-check-input:checked {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.form-check-label {
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.btn-login {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
    border: none;
    color: var(--white-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
}

.btn-login:active {
    transform: translateY(0);
}

/* تذييل تسجيل الدخول */
.login-footer {
    text-align: center;
}

.login-links {
    margin-bottom: 20px;
}

.back-to-site {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
}

.back-to-site:hover {
    color: var(--primary-color);
    transform: translateX(3px);
}

.login-info p {
    font-size: 12px;
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.loading-spinner {
    margin-top: 10px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.loading-spinner i {
    color: var(--success-color);
}

/* معلومات النظام */
.system-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255,255,255,0.9);
    font-size: 14px;
    background: rgba(255,255,255,0.1);
    padding: 10px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.info-item i {
    color: rgba(255,255,255,0.8);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .login-page {
        padding: 15px;
    }
    
    .login-wrapper {
        padding: 30px 25px;
    }
    
    .login-title {
        font-size: 24px;
    }
    
    .logo-section .logo {
        font-size: 20px;
    }
    
    .logo-section .logo i {
        font-size: 28px;
    }
    
    .system-info {
        gap: 15px;
    }
    
    .info-item {
        font-size: 12px;
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .login-wrapper {
        padding: 25px 20px;
    }
    
    .login-title {
        font-size: 22px;
    }
    
    .login-form .form-control {
        padding: 12px 15px;
    }
    
    .btn-login {
        padding: 12px;
        font-size: 14px;
    }
    
    .system-info {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
}

/* تأثيرات إضافية */
.login-wrapper {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.system-info .info-item {
    animation: fadeIn 0.8s ease-out;
    animation-fill-mode: both;
}

.system-info .info-item:nth-child(1) { animation-delay: 0.2s; }
.system-info .info-item:nth-child(2) { animation-delay: 0.4s; }
.system-info .info-item:nth-child(3) { animation-delay: 0.6s; }

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات إمكانية الوصول */
.form-control:focus,
.btn-login:focus,
.back-to-site:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.password-toggle:focus {
    outline: 1px solid var(--secondary-color);
    outline-offset: 1px;
    border-radius: 3px;
}
</style>

<script>
// وظيفة إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

// التركيز على حقل اسم المستخدم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    if (usernameInput && !usernameInput.value) {
        usernameInput.focus();
    }
});

// منع إرسال النموذج عدة مرات
document.querySelector('.login-form').addEventListener('submit', function() {
    const submitBtn = this.querySelector('.btn-login');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
});
</script>

</body>
</html>

<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// إعدادات الصفحة
$page_title = 'الصفحة غير موجودة - 404 | ' . getSetting('site_name', 'موقع الأخبار العربي');
$page_description = 'الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر';

// تسجيل الخطأ 404
error_log("404 Error: " . $_SERVER['REQUEST_URI'] . " - IP: " . $_SERVER['REMOTE_ADDR']);

// إرسال رأس HTTP 404
http_response_code(404);

// تضمين الرأس
include 'includes/header.php';
?>

<div class="container">
    <div class="error-404-page">
        <div class="error-content">
            <!-- رقم الخطأ -->
            <div class="error-number">
                <span class="error-4">4</span>
                <span class="error-0">0</span>
                <span class="error-4-2">4</span>
            </div>
            
            <!-- رسالة الخطأ -->
            <div class="error-message">
                <h1 class="error-title">الصفحة غير موجودة</h1>
                <p class="error-description">
                    عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
                    يمكنك العودة إلى الصفحة الرئيسية أو البحث عن المحتوى الذي تريده.
                </p>
            </div>
            
            <!-- خيارات التنقل -->
            <div class="error-actions">
                <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة للرئيسية
                </a>
                <a href="javascript:history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    الصفحة السابقة
                </a>
            </div>
            
            <!-- نموذج البحث -->
            <div class="error-search">
                <h3>ابحث عن المحتوى</h3>
                <form class="search-form" action="<?php echo SITE_URL; ?>/search.php" method="GET">
                    <div class="search-input-group">
                        <input type="text" name="q" class="search-input" placeholder="ابحث في الموقع..." autocomplete="off">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- اقتراحات -->
        <div class="error-suggestions">
            <div class="row">
                <!-- أحدث المقالات -->
                <div class="col-md-6">
                    <div class="suggestion-section">
                        <h3 class="suggestion-title">
                            <i class="fas fa-newspaper"></i>
                            أحدث المقالات
                        </h3>
                        <?php 
                        $recent_articles = getArticles(5, 0, null, 'published');
                        if (!empty($recent_articles)):
                        ?>
                        <div class="suggestion-list">
                            <?php foreach ($recent_articles as $article): ?>
                            <div class="suggestion-item">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>" class="suggestion-link">
                                    <div class="suggestion-content">
                                        <h4 class="suggestion-item-title">
                                            <?php echo htmlspecialchars(truncateText($article['title'], 60)); ?>
                                        </h4>
                                        <div class="suggestion-meta">
                                            <span class="suggestion-date">
                                                <i class="fas fa-calendar-alt"></i>
                                                <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                            </span>
                                            <?php if ($article['category_name']): ?>
                                            <span class="suggestion-category">
                                                <i class="fas fa-tag"></i>
                                                <?php echo htmlspecialchars($article['category_name']); ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <p class="no-content">لا توجد مقالات متاحة حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- الفئات الشائعة -->
                <div class="col-md-6">
                    <div class="suggestion-section">
                        <h3 class="suggestion-title">
                            <i class="fas fa-tags"></i>
                            تصفح الفئات
                        </h3>
                        <?php 
                        $categories = getCategories();
                        if (!empty($categories)):
                        ?>
                        <div class="categories-grid">
                            <?php foreach ($categories as $category): ?>
                            <?php $article_count = countArticles($category['id'], 'published'); ?>
                            <?php if ($article_count > 0): ?>
                            <div class="category-item">
                                <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $category['slug']; ?>" class="category-link">
                                    <span class="category-color" style="background-color: <?php echo $category['color']; ?>"></span>
                                    <span class="category-name"><?php echo htmlspecialchars($category['name']); ?></span>
                                    <span class="category-count"><?php echo $article_count; ?></span>
                                </a>
                            </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <p class="no-content">لا توجد فئات متاحة حالياً</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="error-info">
            <div class="info-box">
                <h4>هل تواجه مشكلة؟</h4>
                <p>إذا كنت تعتقد أن هذا خطأ، يرجى <a href="<?php echo SITE_URL; ?>/contact.php">التواصل معنا</a> وسنقوم بحل المشكلة في أقرب وقت ممكن.</p>
            </div>
            
            <div class="info-box">
                <h4>نصائح مفيدة</h4>
                <ul>
                    <li>تأكد من صحة الرابط المكتوب</li>
                    <li>استخدم نموذج البحث للعثور على المحتوى</li>
                    <li>تصفح الفئات المختلفة</li>
                    <li>عد إلى الصفحة الرئيسية</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيقات صفحة 404 */
.error-404-page {
    padding: 60px 0;
    text-align: center;
}

.error-content {
    margin-bottom: 60px;
}

/* رقم الخطأ */
.error-number {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 40px;
    font-weight: 900;
    font-size: 120px;
    line-height: 1;
}

.error-4,
.error-0,
.error-4-2 {
    display: inline-block;
    animation: bounce 2s infinite;
}

.error-4 {
    color: var(--secondary-color);
    animation-delay: 0s;
}

.error-0 {
    color: var(--accent-color);
    animation-delay: 0.2s;
    transform: scale(1.2);
}

.error-4-2 {
    color: var(--success-color);
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* رسالة الخطأ */
.error-message {
    margin-bottom: 40px;
}

.error-title {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.error-description {
    font-size: 18px;
    color: var(--text-light);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* أزرار الإجراءات */
.error-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.error-actions .btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
}

.error-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.15);
}

/* نموذج البحث */
.error-search {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    margin-bottom: 50px;
    border: 1px solid var(--border-color);
}

.error-search h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.search-input {
    flex: 1;
    padding: 15px 25px;
    border: none;
    font-size: 16px;
    outline: none;
}

.search-btn {
    padding: 15px 30px;
    background: var(--secondary-color);
    color: var(--white-color);
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
}

.search-btn:hover {
    background: #2980b9;
}

/* الاقتراحات */
.error-suggestions {
    margin-bottom: 50px;
}

.suggestion-section {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
    height: 100%;
    border: 1px solid var(--border-color);
}

.suggestion-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-align: right;
}

.suggestion-title i {
    color: var(--secondary-color);
}

.suggestion-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.suggestion-item {
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.suggestion-item:hover {
    background: var(--light-color);
    border-color: var(--secondary-color);
    transform: translateX(-5px);
}

.suggestion-link {
    text-decoration: none;
    color: inherit;
}

.suggestion-item-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    line-height: 1.4;
    text-align: right;
}

.suggestion-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: var(--text-light);
    justify-content: flex-end;
}

.suggestion-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* شبكة الفئات */
.categories-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.category-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.category-item:hover {
    border-color: var(--secondary-color);
    transform: translateX(-3px);
}

.category-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
}

.category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.category-name {
    flex: 1;
    font-weight: 500;
    text-align: right;
}

.category-count {
    background: var(--light-color);
    color: var(--text-light);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

/* معلومات إضافية */
.error-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    text-align: right;
}

.info-box {
    background: var(--white-color);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.info-box h4 {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.info-box p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 0;
}

.info-box ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-box li {
    padding: 5px 0;
    color: var(--text-light);
    position: relative;
    padding-right: 20px;
}

.info-box li::before {
    content: '•';
    color: var(--secondary-color);
    position: absolute;
    right: 0;
    font-weight: bold;
}

.no-content {
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    padding: 20px;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .error-404-page {
        padding: 40px 0;
    }
    
    .error-number {
        font-size: 80px;
        gap: 10px;
    }
    
    .error-title {
        font-size: 28px;
    }
    
    .error-description {
        font-size: 16px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .search-input-group {
        flex-direction: column;
        border-radius: var(--border-radius);
    }
    
    .search-input,
    .search-btn {
        border-radius: 0;
    }
    
    .search-input {
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .search-btn {
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }
    
    .suggestion-section {
        padding: 20px;
    }
    
    .suggestion-title {
        font-size: 18px;
    }
    
    .suggestion-meta {
        flex-direction: column;
        gap: 5px;
        align-items: flex-end;
    }
    
    .error-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .error-number {
        font-size: 60px;
    }
    
    .error-title {
        font-size: 24px;
    }
    
    .error-search,
    .suggestion-section,
    .info-box {
        padding: 15px;
    }
}
</style>

<?php
// تضمين التذييل
include 'includes/footer.php';
?>

<?php
// محلل RSS لجلب الأخبار تلقائياً

class RSSParser {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    // جلب وتحليل خلاصة RSS
    public function fetchRSSFeed($url) {
        try {
            // إعداد cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Arabic News Site RSS Parser');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $xml_content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code !== 200 || !$xml_content) {
                throw new Exception("فشل في جلب RSS من: $url");
            }
            
            // تحليل XML
            $xml = simplexml_load_string($xml_content);
            if (!$xml) {
                throw new Exception("فشل في تحليل XML من: $url");
            }
            
            return $this->parseRSSItems($xml);
            
        } catch (Exception $e) {
            error_log("RSS Parser Error: " . $e->getMessage());
            return false;
        }
    }
    
    // تحليل عناصر RSS
    private function parseRSSItems($xml) {
        $items = [];
        
        // دعم RSS 2.0
        if (isset($xml->channel->item)) {
            foreach ($xml->channel->item as $item) {
                $items[] = $this->parseRSSItem($item);
            }
        }
        // دعم Atom
        elseif (isset($xml->entry)) {
            foreach ($xml->entry as $entry) {
                $items[] = $this->parseAtomEntry($entry);
            }
        }
        
        return $items;
    }
    
    // تحليل عنصر RSS
    private function parseRSSItem($item) {
        $title = (string) $item->title;
        $description = (string) ($item->description ?: $item->summary);
        $link = (string) $item->link;
        $pubDate = (string) $item->pubDate;
        
        // استخراج الصورة
        $image = '';
        if (isset($item->enclosure) && $item->enclosure['type'] && strpos($item->enclosure['type'], 'image') !== false) {
            $image = (string) $item->enclosure['url'];
        } elseif (isset($item->children('media', true)->thumbnail)) {
            $image = (string) $item->children('media', true)->thumbnail['url'];
        } elseif (isset($item->children('media', true)->content)) {
            $media = $item->children('media', true)->content;
            if (strpos($media['type'], 'image') !== false) {
                $image = (string) $media['url'];
            }
        }
        
        // تنظيف المحتوى
        $description = strip_tags($description);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        
        return [
            'title' => $title,
            'content' => $description,
            'excerpt' => $this->generateExcerpt($description),
            'source_url' => $link,
            'image_url' => $image,
            'published_at' => $this->parseDate($pubDate)
        ];
    }
    
    // تحليل عنصر Atom
    private function parseAtomEntry($entry) {
        $title = (string) $entry->title;
        $content = (string) ($entry->content ?: $entry->summary);
        $link = '';
        
        // البحث عن الرابط
        if (isset($entry->link)) {
            if (is_array($entry->link)) {
                foreach ($entry->link as $l) {
                    if ($l['rel'] == 'alternate' || !$link) {
                        $link = (string) $l['href'];
                        break;
                    }
                }
            } else {
                $link = (string) $entry->link['href'];
            }
        }
        
        $pubDate = (string) ($entry->published ?: $entry->updated);
        
        // تنظيف المحتوى
        $content = strip_tags($content);
        $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
        
        return [
            'title' => $title,
            'content' => $content,
            'excerpt' => $this->generateExcerpt($content),
            'source_url' => $link,
            'image_url' => '',
            'published_at' => $this->parseDate($pubDate)
        ];
    }
    
    // تحليل التاريخ
    private function parseDate($date_string) {
        if (empty($date_string)) {
            return date('Y-m-d H:i:s');
        }
        
        $timestamp = strtotime($date_string);
        if ($timestamp === false) {
            return date('Y-m-d H:i:s');
        }
        
        return date('Y-m-d H:i:s', $timestamp);
    }
    
    // إنشاء مقتطف
    private function generateExcerpt($content, $length = 200) {
        $content = strip_tags($content);
        $content = preg_replace('/\s+/', ' ', $content);
        $content = trim($content);
        
        if (mb_strlen($content, 'UTF-8') <= $length) {
            return $content;
        }
        
        return mb_substr($content, 0, $length, 'UTF-8') . '...';
    }
    
    // حفظ المقال من RSS
    public function saveArticleFromRSS($article_data, $rss_source_id, $category_id = null, $author_id = 1) {
        try {
            // التحقق من وجود المقال
            $this->db->query("SELECT id FROM articles WHERE source_url = ?");
            $this->db->bind(1, $article_data['source_url']);
            $existing = $this->db->single();
            
            if ($existing) {
                return false; // المقال موجود بالفعل
            }
            
            // إنشاء slug
            $slug = $this->generateUniqueSlug($article_data['title']);
            
            // تحميل الصورة إذا كانت متوفرة
            $image_path = '';
            if (!empty($article_data['image_url'])) {
                $image_path = $this->downloadImage($article_data['image_url']);
            }
            
            // حفظ المقال
            $this->db->query("INSERT INTO articles (title, slug, content, excerpt, image, category_id, author_id, 
                                                   source_url, rss_source_id, status, published_at, created_at) 
                             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'published', ?, NOW())");
            
            $this->db->bind(1, $article_data['title']);
            $this->db->bind(2, $slug);
            $this->db->bind(3, $article_data['content']);
            $this->db->bind(4, $article_data['excerpt']);
            $this->db->bind(5, $image_path);
            $this->db->bind(6, $category_id);
            $this->db->bind(7, $author_id);
            $this->db->bind(8, $article_data['source_url']);
            $this->db->bind(9, $rss_source_id);
            $this->db->bind(10, $article_data['published_at']);
            
            return $this->db->execute();
            
        } catch (Exception $e) {
            error_log("Error saving RSS article: " . $e->getMessage());
            return false;
        }
    }
    
    // إنشاء slug فريد
    private function generateUniqueSlug($title) {
        $base_slug = $this->createSlug($title);
        $slug = $base_slug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $base_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    // إنشاء slug
    private function createSlug($text) {
        // تحويل النص العربي
        $text = trim($text);
        $text = preg_replace('/\s+/', '-', $text);
        $text = preg_replace('/[^\p{Arabic}\p{L}\p{N}\-_]/u', '', $text);
        $text = preg_replace('/-+/', '-', $text);
        $text = trim($text, '-');
        
        return strtolower($text);
    }
    
    // التحقق من وجود slug
    private function slugExists($slug) {
        $this->db->query("SELECT id FROM articles WHERE slug = ?");
        $this->db->bind(1, $slug);
        return $this->db->single() !== false;
    }
    
    // تحميل الصورة
    private function downloadImage($image_url) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $image_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Arabic News Site Image Downloader');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $image_data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            curl_close($ch);
            
            if ($http_code !== 200 || !$image_data) {
                return '';
            }
            
            // تحديد امتداد الملف
            $extension = 'jpg';
            if (strpos($content_type, 'png') !== false) {
                $extension = 'png';
            } elseif (strpos($content_type, 'gif') !== false) {
                $extension = 'gif';
            } elseif (strpos($content_type, 'webp') !== false) {
                $extension = 'webp';
            }
            
            // إنشاء مجلد الرفع
            $upload_dir = UPLOADS_PATH . '/rss/' . date('Y/m');
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // حفظ الصورة
            $filename = uniqid() . '.' . $extension;
            $filepath = $upload_dir . '/' . $filename;
            
            if (file_put_contents($filepath, $image_data)) {
                return 'uploads/rss/' . date('Y/m') . '/' . $filename;
            }
            
        } catch (Exception $e) {
            error_log("Error downloading image: " . $e->getMessage());
        }
        
        return '';
    }
    
    // جلب جميع مصادر RSS النشطة
    public function fetchAllRSSFeeds() {
        $sources = getRssSources(true);
        $total_imported = 0;
        
        foreach ($sources as $source) {
            $articles = $this->fetchRSSFeed($source['url']);
            
            if ($articles) {
                foreach ($articles as $article) {
                    if ($this->saveArticleFromRSS($article, $source['id'], $source['category_id'])) {
                        $total_imported++;
                    }
                }
                
                // تحديث آخر جلب
                $this->db->query("UPDATE rss_sources SET last_fetch = NOW() WHERE id = ?");
                $this->db->bind(1, $source['id']);
                $this->db->execute();
            }
        }
        
        return $total_imported;
    }
    
    // جلب RSS لمصدر محدد
    public function fetchRSSSource($source_id) {
        $this->db->query("SELECT * FROM rss_sources WHERE id = ? AND is_active = 1");
        $this->db->bind(1, $source_id);
        $source = $this->db->single();
        
        if (!$source) {
            return false;
        }
        
        $articles = $this->fetchRSSFeed($source['url']);
        $imported = 0;
        
        if ($articles) {
            foreach ($articles as $article) {
                if ($this->saveArticleFromRSS($article, $source['id'], $source['category_id'])) {
                    $imported++;
                }
            }
            
            // تحديث آخر جلب
            $this->db->query("UPDATE rss_sources SET last_fetch = NOW() WHERE id = ?");
            $this->db->bind(1, $source['id']);
            $this->db->execute();
        }
        
        return $imported;
    }
}

// وظيفة مساعدة لتشغيل جلب RSS
function runRSSFetch() {
    global $db;
    
    if (!getSetting('rss_auto_fetch', 1)) {
        return 0;
    }
    
    $parser = new RSSParser($db);
    return $parser->fetchAllRSSFeeds();
}
?>

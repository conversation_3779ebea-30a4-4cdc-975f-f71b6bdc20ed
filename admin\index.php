<?php
// تضمين ملف الإعدادات
require_once dirname(__DIR__) . '/config/config.php';

// التحقق من تسجيل الدخول
requireLogin();

// الحصول على الإحصائيات
$stats = getStats();

// إحصائيات إضافية
$db->query("SELECT COUNT(*) as total FROM users WHERE status = 'active'");
$active_users = $db->single()['total'];

$db->query("SELECT COUNT(*) as total FROM rss_sources WHERE is_active = 1");
$active_rss = $db->single()['total'];

$db->query("SELECT COUNT(*) as total FROM comments WHERE status = 'pending'");
$pending_comments = $db->single()['total'];

$db->query("SELECT COUNT(*) as total FROM articles WHERE status = 'draft'");
$draft_articles = $db->single()['total'];

// أحدث المقالات
$recent_articles = getArticles(5, 0, null, 'published');

// أحدث التعليقات
$db->query("SELECT c.*, a.title as article_title, a.slug as article_slug 
            FROM comments c 
            LEFT JOIN articles a ON c.article_id = a.id 
            ORDER BY c.created_at DESC 
            LIMIT 5");
$recent_comments = $db->resultset();

// المقالات الأكثر مشاهدة
$db->query("SELECT a.*, c.name as category_name 
            FROM articles a 
            LEFT JOIN categories c ON a.category_id = c.id 
            WHERE a.status = 'published' 
            ORDER BY a.views DESC 
            LIMIT 5");
$popular_articles = $db->resultset();

// إعدادات الصفحة
$page_title = 'لوحة التحكم - ' . getSetting('site_name', 'موقع الأخبار العربي');

// تضمين رأس لوحة التحكم
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="content-header">
        <h1 class="page-title">
            <i class="fas fa-tachometer-alt"></i>
            لوحة التحكم
        </h1>
        <p class="page-subtitle">مرحباً <?php echo htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']); ?>، إليك نظرة عامة على موقعك</p>
    </div>
    
    <!-- بطاقات الإحصائيات -->
    <div class="stats-grid">
        <div class="stat-card articles">
            <div class="stat-icon">
                <i class="fas fa-newspaper"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['articles']); ?></div>
                <div class="stat-label">مقال منشور</div>
                <?php if ($draft_articles > 0): ?>
                <div class="stat-extra"><?php echo $draft_articles; ?> مسودة</div>
                <?php endif; ?>
            </div>
            <div class="stat-action">
                <a href="articles.php" class="btn btn-sm btn-primary">إدارة المقالات</a>
            </div>
        </div>
        
        <div class="stat-card categories">
            <div class="stat-icon">
                <i class="fas fa-tags"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['categories']); ?></div>
                <div class="stat-label">فئة</div>
            </div>
            <div class="stat-action">
                <a href="categories.php" class="btn btn-sm btn-success">إدارة الفئات</a>
            </div>
        </div>
        
        <div class="stat-card comments">
            <div class="stat-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['comments']); ?></div>
                <div class="stat-label">تعليق</div>
                <?php if ($pending_comments > 0): ?>
                <div class="stat-extra"><?php echo $pending_comments; ?> في الانتظار</div>
                <?php endif; ?>
            </div>
            <div class="stat-action">
                <a href="comments.php" class="btn btn-sm btn-warning">إدارة التعليقات</a>
            </div>
        </div>
        
        <div class="stat-card views">
            <div class="stat-icon">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($stats['views']); ?></div>
                <div class="stat-label">مشاهدة</div>
            </div>
            <div class="stat-action">
                <a href="analytics.php" class="btn btn-sm btn-info">التحليلات</a>
            </div>
        </div>
        
        <div class="stat-card users">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($active_users); ?></div>
                <div class="stat-label">مستخدم نشط</div>
            </div>
            <div class="stat-action">
                <a href="users.php" class="btn btn-sm btn-secondary">إدارة المستخدمين</a>
            </div>
        </div>
        
        <div class="stat-card rss">
            <div class="stat-icon">
                <i class="fas fa-rss"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?php echo number_format($active_rss); ?></div>
                <div class="stat-label">مصدر RSS</div>
            </div>
            <div class="stat-action">
                <a href="rss.php" class="btn btn-sm btn-orange">إدارة RSS</a>
            </div>
        </div>
    </div>
    
    <!-- الأقسام الرئيسية -->
    <div class="dashboard-sections">
        <div class="row">
            <!-- أحدث المقالات -->
            <div class="col-lg-6">
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-newspaper"></i>
                            أحدث المقالات
                        </h3>
                        <a href="articles.php" class="widget-action">عرض الكل</a>
                    </div>
                    <div class="widget-content">
                        <?php if (!empty($recent_articles)): ?>
                        <div class="articles-list">
                            <?php foreach ($recent_articles as $article): ?>
                            <div class="article-item">
                                <div class="article-info">
                                    <h4 class="article-title">
                                        <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>" target="_blank">
                                            <?php echo htmlspecialchars(truncateText($article['title'], 60)); ?>
                                        </a>
                                    </h4>
                                    <div class="article-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                        </span>
                                        <span class="meta-item">
                                            <i class="fas fa-eye"></i>
                                            <?php echo number_format($article['views']); ?>
                                        </span>
                                        <?php if ($article['category_name']): ?>
                                        <span class="meta-item">
                                            <i class="fas fa-tag"></i>
                                            <?php echo htmlspecialchars($article['category_name']); ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="article-actions">
                                    <a href="edit_article.php?id=<?php echo $article['id']; ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-newspaper"></i>
                            <p>لا توجد مقالات حالياً</p>
                            <a href="add_article.php" class="btn btn-primary">إضافة مقال جديد</a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- أحدث التعليقات -->
            <div class="col-lg-6">
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-comments"></i>
                            أحدث التعليقات
                        </h3>
                        <a href="comments.php" class="widget-action">عرض الكل</a>
                    </div>
                    <div class="widget-content">
                        <?php if (!empty($recent_comments)): ?>
                        <div class="comments-list">
                            <?php foreach ($recent_comments as $comment): ?>
                            <div class="comment-item">
                                <div class="comment-info">
                                    <div class="comment-author">
                                        <strong><?php echo htmlspecialchars($comment['name']); ?></strong>
                                        <span class="comment-status status-<?php echo $comment['status']; ?>">
                                            <?php 
                                            $status_labels = [
                                                'pending' => 'في الانتظار',
                                                'approved' => 'موافق عليه',
                                                'rejected' => 'مرفوض'
                                            ];
                                            echo $status_labels[$comment['status']];
                                            ?>
                                        </span>
                                    </div>
                                    <div class="comment-content">
                                        <?php echo htmlspecialchars(truncateText($comment['content'], 80)); ?>
                                    </div>
                                    <div class="comment-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <?php echo timeAgo($comment['created_at']); ?>
                                        </span>
                                        <?php if ($comment['article_title']): ?>
                                        <span class="meta-item">
                                            <i class="fas fa-newspaper"></i>
                                            <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $comment['article_slug']; ?>" target="_blank">
                                                <?php echo htmlspecialchars(truncateText($comment['article_title'], 30)); ?>
                                            </a>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="comment-actions">
                                    <?php if ($comment['status'] === 'pending'): ?>
                                    <button class="btn btn-sm btn-success" onclick="approveComment(<?php echo $comment['id']; ?>)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="rejectComment(<?php echo $comment['id']; ?>)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-comments"></i>
                            <p>لا توجد تعليقات حالياً</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <!-- المقالات الأكثر مشاهدة -->
            <div class="col-lg-8">
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-fire"></i>
                            المقالات الأكثر مشاهدة
                        </h3>
                    </div>
                    <div class="widget-content">
                        <?php if (!empty($popular_articles)): ?>
                        <div class="popular-articles-table">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المقال</th>
                                        <th>الفئة</th>
                                        <th>المشاهدات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($popular_articles as $article): ?>
                                    <tr>
                                        <td>
                                            <div class="article-cell">
                                                <strong><?php echo htmlspecialchars(truncateText($article['title'], 50)); ?></strong>
                                                <small class="text-muted d-block">
                                                    <?php echo timeAgo($article['published_at'] ?: $article['created_at']); ?>
                                                </small>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($article['category_name']): ?>
                                            <span class="badge badge-secondary"><?php echo htmlspecialchars($article['category_name']); ?></span>
                                            <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($article['views']); ?></strong>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>" 
                                                   class="btn btn-sm btn-outline-info" target="_blank" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_article.php?id=<?php echo $article['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-fire"></i>
                            <p>لا توجد مقالات حالياً</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- إجراءات سريعة -->
            <div class="col-lg-4">
                <div class="dashboard-widget">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-bolt"></i>
                            إجراءات سريعة
                        </h3>
                    </div>
                    <div class="widget-content">
                        <div class="quick-actions">
                            <a href="add_article.php" class="quick-action-btn">
                                <i class="fas fa-plus"></i>
                                <span>إضافة مقال جديد</span>
                            </a>
                            
                            <a href="categories.php" class="quick-action-btn">
                                <i class="fas fa-tags"></i>
                                <span>إدارة الفئات</span>
                            </a>
                            
                            <a href="rss.php" class="quick-action-btn">
                                <i class="fas fa-rss"></i>
                                <span>جلب من RSS</span>
                            </a>
                            
                            <a href="settings.php" class="quick-action-btn">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات الموقع</span>
                            </a>
                            
                            <a href="<?php echo SITE_URL; ?>" class="quick-action-btn" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                <span>عرض الموقع</span>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="dashboard-widget mt-3">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات النظام
                        </h3>
                    </div>
                    <div class="widget-content">
                        <div class="system-info">
                            <div class="info-item">
                                <span class="info-label">إصدار PHP:</span>
                                <span class="info-value"><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">قاعدة البيانات:</span>
                                <span class="info-value">MySQL</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">آخر تسجيل دخول:</span>
                                <span class="info-value"><?php echo formatDate($_SESSION['last_login'] ?? date('Y-m-d H:i:s'), 'd/m/Y H:i'); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">دورك:</span>
                                <span class="info-value">
                                    <?php 
                                    $role_labels = [
                                        'admin' => 'مدير',
                                        'editor' => 'محرر',
                                        'author' => 'كاتب'
                                    ];
                                    echo $role_labels[$_SESSION['user_role']] ?? $_SESSION['user_role'];
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين تذييل لوحة التحكم
include 'includes/admin_footer.php';
?>

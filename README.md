# موقع الأخبار العربي الشامل

موقع إخباري عربي شامل مبني بـ PHP مع دعم كامل للغة العربية ونظام إدارة محتوى متقدم.

## المميزات

### 🌟 المميزات الأساسية
- **دعم كامل للغة العربية** مع تصميم RTL
- **نظام إدارة محتوى متقدم** مع لوحة تحكم شاملة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **نظام RSS متقدم** لجلب الأخبار تلقائياً
- **نظام تعليقات** مع إدارة ومراجعة
- **محرك بحث قوي** مع فلترة متقدمة
- **نظام فئات** منظم ومرن
- **إحصائيات شاملة** ولوحة تحكم تفاعلية

### 🔧 المميزات التقنية
- **PHP 7.4+** مع أفضل الممارسات
- **قاعدة بيانات MySQL** محسنة للأداء
- **تصميم أمني متقدم** مع حماية من الثغرات
- **SEO محسن** لمحركات البحث
- **تخزين مؤقت ذكي** لتحسين الأداء
- **نظام صلاحيات متدرج** (مدير، محرر، كاتب)

### 📱 تجربة المستخدم
- **تصميم عصري وجذاب** مع ألوان متناسقة
- **تنقل سهل وسريع** مع قوائم ذكية
- **بحث فوري** مع اقتراحات تلقائية
- **مشاركة اجتماعية** على جميع المنصات
- **تحميل سريع** مع تحسينات الأداء

## متطلبات النظام

### الحد الأدنى
- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache**: 2.4+ مع mod_rewrite
- **مساحة القرص**: 100 ميجابايت على الأقل
- **الذاكرة**: 256 ميجابايت RAM

### المستحسن
- **PHP**: 8.0 أو أحدث
- **MySQL**: 8.0 أو أحدث
- **Apache**: 2.4+ مع mod_rewrite, mod_deflate, mod_expires
- **مساحة القرص**: 1 جيجابايت أو أكثر
- **الذاكرة**: 512 ميجابايت RAM أو أكثر

### الإضافات المطلوبة لـ PHP
```
- mysqli
- pdo_mysql
- gd
- curl
- mbstring
- json
- xml
- zip
```

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-username/arabic-news-site.git
cd arabic-news-site
```

### 2. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات جديدة في MySQL
2. انسخ ملف `config/config.example.php` إلى `config/config.php`
3. عدّل إعدادات قاعدة البيانات في `config/config.php`

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. تشغيل التثبيت
1. ارفع الملفات إلى الخادم
2. اذهب إلى `http://yoursite.com/install/setup.php`
3. اتبع تعليمات التثبيت

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 assets/images/
chmod 644 .htaccess
```

### 5. تسجيل الدخول
- **الرابط**: `http://yoursite.com/admin/`
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: غيّر كلمة المرور فوراً بعد التثبيت!

## الاستخدام

### لوحة التحكم
- **الرئيسية**: `/admin/` - نظرة عامة وإحصائيات
- **المقالات**: `/admin/articles.php` - إدارة المقالات
- **الفئات**: `/admin/categories.php` - إدارة الفئات
- **التعليقات**: `/admin/comments.php` - مراجعة التعليقات
- **RSS**: `/admin/rss.php` - إدارة مصادر RSS
- **المستخدمين**: `/admin/users.php` - إدارة المستخدمين
- **الإعدادات**: `/admin/settings.php` - إعدادات الموقع

### إضافة محتوى
1. **إضافة فئة جديدة**:
   - اذهب إلى الفئات → إضافة جديد
   - أدخل اسم الفئة ووصفها
   - اختر لون مميز للفئة

2. **إضافة مقال**:
   - اذهب إلى المقالات → إضافة جديد
   - أدخل العنوان والمحتوى
   - اختر الفئة والصورة
   - حدد حالة النشر

3. **إعداد RSS**:
   - اذهب إلى RSS → إضافة مصدر
   - أدخل رابط RSS والفئة
   - فعّل الجلب التلقائي

## التخصيص

### تغيير التصميم
- **الألوان**: عدّل متغيرات CSS في `assets/css/style.css`
- **الخطوط**: غيّر خط Cairo في بداية ملف CSS
- **الشعار**: ارفع شعارك في إعدادات الموقع

### إضافة وظائف
- **الملفات الأساسية**: `config/functions.php`
- **قاعدة البيانات**: `config/database.php`
- **الصفحات**: أنشئ ملفات PHP جديدة

### تخصيص القوالب
- **الرأس**: `includes/header.php`
- **التذييل**: `includes/footer.php`
- **الشريط الجانبي**: `includes/sidebar.php`

## الأمان

### إعدادات الأمان
- تغيير كلمات المرور الافتراضية
- تحديث PHP وMySQL بانتظام
- استخدام HTTPS
- نسخ احتياطية دورية

### الحماية من الهجمات
- حماية من SQL Injection
- حماية من XSS
- حماية من CSRF
- تشفير كلمات المرور
- تحديد معدل الطلبات

## الصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p database_name > backup.sql

# نسخ احتياطي للملفات
tar -czf website_backup.tar.gz /path/to/website/
```

### التحديثات
1. انسخ الملفات الجديدة
2. شغّل سكريبت التحديث إن وجد
3. امسح التخزين المؤقت
4. اختبر الموقع

### مراقبة الأداء
- راقب استخدام الذاكرة
- تحقق من سرعة التحميل
- راجع سجلات الأخطاء
- راقب استخدام قاعدة البيانات

## استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بقاعدة البيانات**
```
تحقق من إعدادات قاعدة البيانات في config/config.php
تأكد من تشغيل خدمة MySQL
تحقق من صلاحيات المستخدم
```

**صفحة 404**
```
تأكد من تفعيل mod_rewrite في Apache
تحقق من ملف .htaccess
تأكد من الصلاحيات الصحيحة
```

**مشاكل الرفع**
```
تحقق من صلاحيات مجلد uploads/
زد حجم upload_max_filesize في PHP
تأكد من وجود مساحة كافية
```

## المساهمة

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إضافة التحسينات
4. إرسال Pull Request

### إرشادات المساهمة
- اتبع معايير الكود الموجودة
- أضف تعليقات باللغة العربية
- اختبر التغييرات قبل الإرسال
- حدّث الوثائق عند الحاجة

## الدعم

### الحصول على المساعدة
- **الوثائق**: راجع هذا الملف
- **المشاكل**: أنشئ Issue في GitHub
- **المناقشات**: استخدم قسم Discussions

### الإبلاغ عن الأخطاء
عند الإبلاغ عن خطأ، يرجى تضمين:
- وصف مفصل للمشكلة
- خطوات إعادة الإنتاج
- رسائل الخطأ
- معلومات البيئة (PHP، MySQL، إلخ)

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## الشكر والتقدير

- **Font Awesome** للأيقونات الرائعة
- **Cairo Font** للخط العربي الجميل
- **مجتمع PHP** للدعم المستمر
- **جميع المساهمين** في تطوير المشروع

---

**تم تطوير هذا المشروع بـ ❤️ للمجتمع العربي**

للمزيد من المعلومات، زوروا [موقع المشروع](https://github.com/your-username/arabic-news-site)
